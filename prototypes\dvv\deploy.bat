@echo off
REM Deployment script for Windows LAN deployment

echo Starting PQ Config Validator for LAN deployment...

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.8 or later
    pause
    exit /b 1
)

REM Check if virtual environment exists
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Install dependencies
echo Installing dependencies...
pip install -r requirements.txt

REM Set environment variables for LAN deployment
set HOST=0.0.0.0
set PORT=8000
set LOG_LEVEL=INFO
set DEBUG=false

REM Get local IP address
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4 Address"') do (
    set LOCAL_IP=%%a
)

echo.
echo ========================================
echo DVV
echo ========================================
echo Server will be accessible at:
echo   Local:    http://localhost:8000
echo   Network:  http://%LOCAL_IP%:8000
echo ========================================
echo.
echo Press Ctrl+C to stop the server
echo.

REM Start the application
python run.py --host %HOST% --port %PORT% --log-level %LOG_LEVEL%

pause
