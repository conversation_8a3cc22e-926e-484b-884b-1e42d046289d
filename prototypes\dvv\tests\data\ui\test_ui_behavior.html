<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI Behavior Test - PQ Config Validator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .log-info-section {
            background-color: #e8f4fd;
            border: 1px solid #bee5eb;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .hidden {
            display: none;
        }
        .visible {
            display: block;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>UI Behavior Test - Log Info Section Visibility</h1>
        
        <div class="test-section">
            <h2>Test: Log Info Section Visibility Control</h2>
            <p>This test verifies that the log info section is properly hidden/shown based on processing mode.</p>
            <p><strong>Expected Behavior:</strong></p>
            <ul>
                <li><strong>Parameter Validation Mode:</strong> should show log info section</li>
                <li><strong>Log Visualization Mode:</strong> should hide log info section (regardless of specific processor)</li>
            </ul>
            <p><em>This approach is more maintainable as it doesn't depend on specific processor names.</em></p>
            
            <div class="log-info-section" id="logInfoSection">
                <h3>Log Information</h3>
                <p><strong>Total Frames:</strong> <span id="totalFrames">-</span></p>
                <p><strong>Config Parameters:</strong> <span id="configParams">-</span></p>
                <p><strong>DM Parameters:</strong> <span id="dmParams">-</span></p>
                <p><strong>Total Parameters:</strong> <span id="totalParams">-</span></p>
            </div>
            
            <div class="controls">
                <button onclick="testParameterValidation()">Test Parameter Validation Mode</button>
                <button onclick="testLogVisualization()">Test Log Visualization Mode</button>
                <button onclick="testNewLogProcessor()">Test New Log Processor (Future-proof)</button>
            </div>
            
            <div id="testStatus" class="status"></div>
        </div>
    </div>

    <script>
        // Mock results object for testing
        let results = {
            processing_mode: 'parameter_validation',
            processor_used: 'pq_config_validator',
            log_info: {
                total_frames: 100,
                config_parameters: 25,
                dm_parameters: 15,
                total_parameters: 40
            }
        };

        function updateLogInfoVisibility() {
            const logInfoSection = document.getElementById('logInfoSection');

            // New condition: Show log info section only for parameter validation mode
            if (results.log_info && results.processing_mode === 'parameter_validation') {
                logInfoSection.classList.remove('hidden');
                logInfoSection.classList.add('visible');

                // Update log info display
                document.getElementById('totalFrames').textContent = results.log_info.total_frames || '-';
                document.getElementById('configParams').textContent = results.log_info.config_parameters || '-';
                document.getElementById('dmParams').textContent = results.log_info.dm_parameters || '-';
                document.getElementById('totalParams').textContent = results.log_info.total_parameters || '-';
            } else {
                logInfoSection.classList.add('hidden');
                logInfoSection.classList.remove('visible');
            }
        }

        function testParameterValidation() {
            results.processing_mode = 'parameter_validation';
            results.processor_used = 'pq_config_validator';
            updateLogInfoVisibility();

            const status = document.getElementById('testStatus');
            const isVisible = document.getElementById('logInfoSection').classList.contains('visible');

            if (isVisible) {
                status.className = 'status success';
                status.textContent = '✅ PASS: Log info section is correctly shown for parameter_validation mode';
            } else {
                status.className = 'status error';
                status.textContent = '❌ FAIL: Log info section should be shown for parameter_validation mode';
            }
        }

        function testLogVisualization() {
            results.processing_mode = 'log_visualization';
            results.processor_used = 'grid_comparison'; // Could be any log processor
            updateLogInfoVisibility();

            const status = document.getElementById('testStatus');
            const isHidden = document.getElementById('logInfoSection').classList.contains('hidden');

            if (isHidden) {
                status.className = 'status success';
                status.textContent = '✅ PASS: Log info section is correctly hidden for log_visualization mode';
            } else {
                status.className = 'status error';
                status.textContent = '❌ FAIL: Log info section should be hidden for log_visualization mode';
            }
        }

        function testNewLogProcessor() {
            results.processing_mode = 'log_visualization';
            results.processor_used = 'future_new_processor'; // Simulate a new processor
            updateLogInfoVisibility();

            const status = document.getElementById('testStatus');
            const isHidden = document.getElementById('logInfoSection').classList.contains('hidden');

            if (isHidden) {
                status.className = 'status success';
                status.textContent = '✅ PASS: Log info section correctly hidden for new processor (future-proof)';
            } else {
                status.className = 'status error';
                status.textContent = '❌ FAIL: Log info section should be hidden for any log_visualization processor';
            }
        }

        // Initialize the test
        document.addEventListener('DOMContentLoaded', function() {
            const status = document.getElementById('testStatus');
            status.className = 'status info';
            status.textContent = 'Ready to test. Click buttons above to test different processing modes. The new approach uses processing_mode instead of specific processor names for better maintainability.';

            // Show initial state (parameter validation mode)
            updateLogInfoVisibility();
        });
    </script>
</body>
</html>
