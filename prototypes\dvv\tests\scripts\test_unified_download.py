#!/usr/bin/env python3
"""
Test script for unified download button functionality
Author: <PERSON>
"""

import requests
import time
import json
from pathlib import Path

def test_unified_download_functionality():
    """Test the unified download button functionality across all applications"""
    
    base_url = "http://localhost:8000"
    
    print("Testing Unified Download Button Functionality")
    print("=" * 50)
    
    # Test 1: Register a user for PQ Config Validator
    print("\n1. Testing PQ Config Validator download...")
    
    registration_data = {
        "user_id": "test_unified_download_user",
        "selected_app": "pq_config_validator"
    }
    
    try:
        # Register user
        response = requests.post(f"{base_url}/register", json=registration_data)
        if response.status_code == 200:
            data = response.json()
            session_id = data.get("session_id")
            print(f"✅ User registered successfully. Session ID: {session_id}")
            
            # Test file upload
            test_file_path = Path("test_files/sample_config.txt")
            if test_file_path.exists():
                with open(test_file_path, 'rb') as f:
                    files = {'file': f}
                    upload_response = requests.post(f"{base_url}/api/upload/{session_id}", files=files)
                    
                if upload_response.status_code == 200:
                    print("✅ File uploaded successfully")
                    
                    # Test download functionality
                    download_response = requests.get(f"{base_url}/api/download-results/{session_id}?format=html")
                    if download_response.status_code == 200:
                        print("✅ HTML download works correctly")
                        print(f"   Content-Type: {download_response.headers.get('content-type')}")
                        print(f"   Content-Length: {len(download_response.content)} bytes")
                    else:
                        print(f"❌ HTML download failed: {download_response.status_code}")
                        
                    # Test that markdown is no longer supported
                    markdown_response = requests.get(f"{base_url}/api/download-results/{session_id}?format=markdown")
                    if markdown_response.status_code == 422:  # Validation error expected
                        print("✅ Markdown format correctly rejected (HTML-only restriction working)")
                    else:
                        print(f"❌ Markdown format should be rejected but got: {markdown_response.status_code}")
                        
                else:
                    print(f"❌ File upload failed: {upload_response.status_code}")
            else:
                print(f"❌ Test file not found: {test_file_path}")
        else:
            print(f"❌ User registration failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing PQ Config Validator: {e}")
    
    # Test 2: Test Grid Comparison Plotter
    print("\n2. Testing Grid Comparison Plotter download...")
    
    grid_registration_data = {
        "user_id": "test_grid_download_user",
        "selected_app": "grid_comparison_plotter"
    }
    
    try:
        # Register user for grid comparison
        response = requests.post(f"{base_url}/register", json=grid_registration_data)
        if response.status_code == 200:
            data = response.json()
            session_id = data.get("session_id")
            print(f"✅ Grid comparison user registered. Session ID: {session_id}")
            
            # Test with TC Flash log file
            tc_flash_file = Path("sample_tc_flash_log.txt")
            if tc_flash_file.exists():
                with open(tc_flash_file, 'rb') as f:
                    files = {'file': f}
                    upload_response = requests.post(f"{base_url}/api/upload/{session_id}", files=files)
                    
                if upload_response.status_code == 200:
                    upload_data = upload_response.json()
                    print("✅ TC Flash log uploaded successfully")
                    
                    # Check if output files were generated
                    if upload_data.get('output_files'):
                        print(f"✅ Generated {len(upload_data['output_files'])} output files")
                        for file in upload_data['output_files']:
                            print(f"   - {file}")
                    else:
                        print("ℹ️  No output files in response (may be generated asynchronously)")
                        
                else:
                    print(f"❌ TC Flash log upload failed: {upload_response.status_code}")
            else:
                print(f"❌ TC Flash test file not found: {tc_flash_file}")
        else:
            print(f"❌ Grid comparison registration failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing Grid Comparison Plotter: {e}")
    
    # Test 3: Test Firmware Log Visualizer
    print("\n3. Testing Firmware Log Visualizer download...")
    
    firmware_registration_data = {
        "user_id": "test_firmware_download_user",
        "selected_app": "firmware_log_visualizer"
    }
    
    try:
        # Register user for firmware log visualizer
        response = requests.post(f"{base_url}/register", json=firmware_registration_data)
        if response.status_code == 200:
            data = response.json()
            session_id = data.get("session_id")
            print(f"✅ Firmware log visualizer user registered. Session ID: {session_id}")
            
            # Test with firmware log file
            firmware_file = Path("test_firmware_log.txt")
            if firmware_file.exists():
                with open(firmware_file, 'rb') as f:
                    files = {'file': f}
                    upload_response = requests.post(f"{base_url}/api/upload/{session_id}", files=files)
                    
                if upload_response.status_code == 200:
                    upload_data = upload_response.json()
                    print("✅ Firmware log uploaded successfully")
                    
                    # Check if output files were generated
                    if upload_data.get('output_files'):
                        print(f"✅ Generated {len(upload_data['output_files'])} output files")
                        for file in upload_data['output_files']:
                            print(f"   - {file}")
                    else:
                        print("ℹ️  No output files in response (may be generated asynchronously)")
                        
                else:
                    print(f"❌ Firmware log upload failed: {upload_response.status_code}")
            else:
                print(f"❌ Firmware test file not found: {firmware_file}")
        else:
            print(f"❌ Firmware log visualizer registration failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing Firmware Log Visualizer: {e}")
    
    print("\n" + "=" * 50)
    print("Unified Download Button Test Complete!")
    print("\nTo manually test the UI:")
    print("1. Open http://localhost:8000 in your browser")
    print("2. Register with different applications")
    print("3. Upload appropriate files")
    print("4. Verify download buttons have consistent styling")
    print("5. Test download functionality")

if __name__ == "__main__":
    test_unified_download_functionality()
