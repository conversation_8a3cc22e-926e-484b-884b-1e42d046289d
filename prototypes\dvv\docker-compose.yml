# PQ Config Validator Docker Compose Configuration
# Author: <PERSON>
# Version: 1.0.0

version: '3.8'

services:
  pq-config-validator:
    build:
      context: .
      dockerfile: Dockerfile
    image: pq-config-validator:latest
    container_name: pq-config-validator
    restart: unless-stopped

    # Port mapping
    ports:
      - "8000:8000"  # Default mapping
      # Alternative ports for different environments:
      # - "8001:8000"  # Development
      # - "8080:8000"  # Alternative production

    # Environment variables
    environment:
      - HOST=0.0.0.0
      - PORT=8000
      - LOG_LEVEL=INFO
      - DEBUG=false
      - SESSION_TIMEOUT_HOURS=24
      - MAX_FILE_SIZE=10485760  # 10MB in bytes

    # Volume mounts for persistent data
    volumes:
      # Persistent data directories
      - ./data/uploads:/app/uploads
      - ./data/sessions:/app/sessions
      - ./data/logs:/app/logs

      # Configuration files (optional overrides)
      # - ./config/config.py:/app/config.py:ro

      # External tool binary (if needed)
      # - ./bin/dtv_config_lib_test.exe:/app/bin/dtv_config_lib_test.exe:ro

    # Network configuration
    networks:
      - pq-validator-network

    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 128M

    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

# Network configuration
networks:
  pq-validator-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Volume definitions for named volumes (alternative to bind mounts)
volumes:
  pq-uploads:
    driver: local
  pq-sessions:
    driver: local
  pq-logs:
    driver: local
