#!/usr/bin/env python3
"""
Grid Comparison Plotter for TC Flash PQ Log Analysis

This script creates a comprehensive grid layout comparison plot that displays
all parameter comparisons in a single image file.

Usage:
    python grid_comparison_plotter.py [log_file_path]
    python grid_comparison_plotter.py --help

The log file should contain TC Flash PQ data with "Config values:" and "DM values:" sections.
"""

import re
import matplotlib.pyplot as plt
import numpy as np
from collections import defaultdict
import os
import math
import argparse
import sys
from log_parser_plotter import parse_log_file

def validate_log_file(log_filename):
    """
    Validate that the log file exists and contains expected TC Flash PQ sections.

    Args:
        log_filename (str): Path to the log file

    Returns:
        bool: True if file is valid, False otherwise

    Raises:
        SystemExit: If file doesn't exist or is invalid
    """
    # Check if file exists
    if not os.path.exists(log_filename):
        print(f"Error: Log file '{log_filename}' not found!")
        print("Please check the file path and try again.")
        sys.exit(1)

    # Check if file contains expected sections
    try:
        with open(log_filename, 'r') as file:
            content = file.read()

        has_config = "Config values:" in content
        has_dm = "DM values:" in content
        has_fallback = "fallback: 0" in content

        if not (has_config and has_dm and has_fallback):
            print(f"Error: '{log_filename}' does not appear to be a valid TC Flash PQ log file!")
            print("Expected sections not found:")
            if not has_config:
                print("  - Missing 'Config values:' section")
            if not has_dm:
                print("  - Missing 'DM values:' section")
            if not has_fallback:
                print("  - Missing 'fallback: 0' frame markers")
            print("\nThe log file should contain TC Flash PQ data with Config and DM value sections.")
            sys.exit(1)

        return True

    except Exception as e:
        print(f"Error reading log file '{log_filename}': {e}")
        sys.exit(1)

def parse_arguments():
    """
    Parse command-line arguments.

    Returns:
        argparse.Namespace: Parsed arguments
    """
    parser = argparse.ArgumentParser(
        description='Create comprehensive grid comparison plots for TC Flash PQ log analysis',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s                                    # Use default log file
  %(prog)s my_log_file.txt                   # Use custom log file
  %(prog)s /path/to/tc_flash_log.txt         # Use log file with full path

Log File Format:
  The log file should contain TC Flash PQ data with repeating sections:
  - "fallback: 0" (frame markers)
  - "Config values:" followed by parameter lines
  - "DM values:" followed by parameter lines

  Parameter format examples:
    Config: "  intensity_level:                         10"
    DM:     "  LmOn                                      1 (scale  0)"
        """
    )

    parser.add_argument(
        'log_file',
        nargs='?',
        default='TC_Flash_first_few_frames_pq_log.txt',
        help='Path to the TC Flash PQ log file (default: TC_Flash_first_few_frames_pq_log.txt)'
    )

    parser.add_argument(
        '--output-dir',
        default='plots',
        help='Output directory for generated plots (default: plots)'
    )

    return parser.parse_args()

def identify_all_parameters(config_data, dm_data):
    """
    Identify all parameters and their relationships for grid display.
    
    Returns:
        list: List of plot specifications for the grid
    """
    plot_specs = []
    
    # Define parameter pairs for comparison using correct mappings
    parameter_pairs = [
        ('PrecisionRenderingStrength', 'LocalMappingStrength', 'PrecisionRenderingStrength Comparison'),
        ('DBrightness', 'dBrightness', 'DBrightness Comparison'),
        ('DBrightness_PR_on', 'dBrightnessPRon', 'DBrightness_PR_on Comparison'),
        ('DContrast', 'dContrast', 'DContrast Comparison'),
        ('DSaturation', 'dSaturation', 'DSaturation Comparison'),
        ('DLocalContrast', 'dLocalContrast', 'DLocalContrast Comparison'),
    ]
    
    # Add comparison pairs
    for config_param, dm_param, title in parameter_pairs:
        if config_param in config_data and dm_param in dm_data:
            plot_specs.append({
                'type': 'comparison',
                'title': title,
                'config_param': config_param,
                'dm_param': dm_param,
                'config_values': config_data[config_param],
                'dm_values': dm_data[dm_param]
            })
    
    # Track which parameters are already used in comparisons
    used_config = {spec['config_param'] for spec in plot_specs if spec['type'] == 'comparison'}
    used_dm = {spec['dm_param'] for spec in plot_specs if spec['type'] == 'comparison'}
    
    # Add Config-only parameters (prioritize varying parameters)
    config_only = [(param, values) for param, values in config_data.items() 
                   if param not in used_config]
    config_only.sort(key=lambda x: len(set(x[1])), reverse=True)  # Varying parameters first
    
    for param, values in config_only:
        plot_specs.append({
            'type': 'config_only',
            'title': f'Config: {param}',
            'config_param': param,
            'dm_param': None,
            'config_values': values,
            'dm_values': None
        })
    
    # Add DM-only parameters (prioritize varying parameters)
    dm_only = [(param, values) for param, values in dm_data.items() 
               if param not in used_dm]
    dm_only.sort(key=lambda x: len(set(x[1])), reverse=True)  # Varying parameters first
    
    for param, values in dm_only:
        plot_specs.append({
            'type': 'dm_only',
            'title': f'DM: {param}',
            'config_param': None,
            'dm_param': param,
            'config_values': None,
            'dm_values': values
        })
    
    return plot_specs

def calculate_grid_layout(n_plots):
    """
    Calculate optimal grid layout for given number of plots.
    
    Returns:
        tuple: (n_rows, n_cols)
    """
    if n_plots <= 4:
        return (2, 2)
    elif n_plots <= 6:
        return (2, 3)
    elif n_plots <= 9:
        return (3, 3)
    elif n_plots <= 12:
        return (3, 4)
    elif n_plots <= 16:
        return (4, 4)
    elif n_plots <= 20:
        return (4, 5)
    else:
        # For larger numbers, aim for roughly square layout
        n_cols = math.ceil(math.sqrt(n_plots))
        n_rows = math.ceil(n_plots / n_cols)
        return (n_rows, n_cols)

def create_grid_comparison_plot(config_data, dm_data, output_dir="plots"):
    """
    Create a comprehensive grid layout comparison plot.
    """
    # Get all plot specifications
    plot_specs = identify_all_parameters(config_data, dm_data)
    n_plots = len(plot_specs)
    
    print(f"Creating grid comparison plot with {n_plots} subplots...")
    
    # Calculate grid layout
    n_rows, n_cols = calculate_grid_layout(n_plots)
    print(f"Using {n_rows}x{n_cols} grid layout")
    
    # Create the figure with appropriate size
    fig_width = max(20, n_cols * 4)
    fig_height = max(12, n_rows * 3)
    
    fig, axes = plt.subplots(n_rows, n_cols, figsize=(fig_width, fig_height))
    fig.suptitle('Comprehensive Parameter Comparison: Config vs DM Values', 
                 fontsize=20, fontweight='bold', y=0.98)
    
    # Handle different subplot configurations
    if n_rows == 1 and n_cols == 1:
        axes = [axes]
    elif n_rows == 1 or n_cols == 1:
        axes = axes.flatten()
    else:
        axes = axes.flatten()
    
    # Get frame numbers (assuming all parameters have same length)
    frames = list(range(1, len(next(iter(config_data.values()))) + 1))
    
    # Create each subplot
    for i, spec in enumerate(plot_specs):
        ax = axes[i]
        
        if spec['type'] == 'comparison':
            # Plot both Config and DM values
            ax.plot(frames, spec['config_values'], 
                   color='blue', linewidth=2, marker='o', markersize=2,
                   label='Config', alpha=0.8)
            ax.plot(frames, spec['dm_values'],
                   color='red', linewidth=2, marker='s', markersize=2,
                   linestyle='--', label='DM', alpha=0.8)
            
        elif spec['type'] == 'config_only':
            # Plot only Config values
            ax.plot(frames, spec['config_values'],
                   color='blue', linewidth=2, marker='o', markersize=2,
                   label='Config', alpha=0.8)
            
        elif spec['type'] == 'dm_only':
            # Plot only DM values
            ax.plot(frames, spec['dm_values'],
                   color='red', linewidth=2, marker='s', markersize=2,
                   linestyle='--', label='DM', alpha=0.8)
        
        # Format subplot
        ax.set_title(spec['title'], fontsize=11, fontweight='bold', pad=10)
        ax.set_xlabel('Frame', fontsize=9)
        ax.set_ylabel('Value', fontsize=9)
        ax.grid(True, alpha=0.3)
        ax.tick_params(axis='both', which='major', labelsize=8)
        
        # Add trend lines for varying parameters
        if spec['type'] == 'comparison':
            config_varies = len(set(spec['config_values'])) > 1
            dm_varies = len(set(spec['dm_values'])) > 1
            
            if config_varies:
                z = np.polyfit(frames, spec['config_values'], 1)
                p = np.poly1d(z)
                ax.plot(frames, p(frames), color='blue', linestyle=':', alpha=0.5, linewidth=1)
            
            if dm_varies:
                z = np.polyfit(frames, spec['dm_values'], 1)
                p = np.poly1d(z)
                ax.plot(frames, p(frames), color='red', linestyle=':', alpha=0.5, linewidth=1)
                
        else:
            # Single parameter trend line
            values = spec['config_values'] if spec['config_values'] is not None else spec['dm_values']
            if len(set(values)) > 1:
                color = 'blue' if spec['type'] == 'config_only' else 'red'
                z = np.polyfit(frames, values, 1)
                p = np.poly1d(z)
                ax.plot(frames, p(frames), color=color, linestyle=':', alpha=0.5, linewidth=1)
        
        # Add value range as text
        if spec['type'] == 'comparison':
            config_range = f"[{min(spec['config_values'])}-{max(spec['config_values'])}]"
            dm_range = f"[{min(spec['dm_values'])}-{max(spec['dm_values'])}]"
            range_text = f"Config: {config_range}\nDM: {dm_range}"
        else:
            values = spec['config_values'] if spec['config_values'] is not None else spec['dm_values']
            value_range = f"[{min(values)}-{max(values)}]"
            section = "Config" if spec['type'] == 'config_only' else "DM"
            range_text = f"{section}: {value_range}"
        
        ax.text(0.02, 0.98, range_text, transform=ax.transAxes, fontsize=7,
                verticalalignment='top', bbox=dict(boxstyle='round,pad=0.3', 
                facecolor='white', alpha=0.8))
    
    # Hide unused subplots
    for i in range(n_plots, len(axes)):
        axes[i].set_visible(False)
    
    # Create master legend
    legend_elements = [
        plt.Line2D([0], [0], color='blue', linewidth=2, marker='o', markersize=4,
                   label='Config Values (solid line, circles)'),
        plt.Line2D([0], [0], color='red', linewidth=2, linestyle='--', marker='s', markersize=4,
                   label='DM Values (dashed line, squares)'),
        plt.Line2D([0], [0], color='gray', linewidth=1, linestyle=':', alpha=0.7,
                   label='Trend Lines (dotted)')
    ]
    
    fig.legend(handles=legend_elements, loc='upper center', bbox_to_anchor=(0.5, 0.02),
               ncol=3, fontsize=12, frameon=True, fancybox=True, shadow=True)
    
    # Adjust layout to prevent overlap
    plt.tight_layout(rect=[0, 0.05, 1, 0.96])
    
    # Save the plot
    os.makedirs(output_dir, exist_ok=True)
    filepath = os.path.join(output_dir, "all_parameters_comparison_grid.png")
    plt.savefig(filepath, dpi=500, bbox_inches='tight', facecolor='white')
    plt.close()
    
    print(f"Saved comprehensive grid plot: all_parameters_comparison_grid.png")
    
    return n_plots

def print_grid_summary(config_data, dm_data):
    """
    Print a summary of what will be included in the grid plot.
    """
    plot_specs = identify_all_parameters(config_data, dm_data)
    
    print("\n" + "="*60)
    print("GRID PLOT SUMMARY")
    print("="*60)
    
    comparisons = [spec for spec in plot_specs if spec['type'] == 'comparison']
    config_only = [spec for spec in plot_specs if spec['type'] == 'config_only']
    dm_only = [spec for spec in plot_specs if spec['type'] == 'dm_only']
    
    print(f"\nParameter Comparisons ({len(comparisons)}):")
    for spec in comparisons:
        config_varies = len(set(spec['config_values'])) > 1
        dm_varies = len(set(spec['dm_values'])) > 1
        print(f"  {spec['title']}: Config {'varies' if config_varies else 'constant'}, "
              f"DM {'varies' if dm_varies else 'constant'}")
    
    print(f"\nConfig-Only Parameters ({len(config_only)}):")
    for spec in config_only:
        varies = len(set(spec['config_values'])) > 1
        print(f"  {spec['config_param']}: {'varies' if varies else 'constant'}")
    
    print(f"\nDM-Only Parameters ({len(dm_only)}):")
    for spec in dm_only:
        varies = len(set(spec['dm_values'])) > 1
        print(f"  {spec['dm_param']}: {'varies' if varies else 'constant'}")
    
    n_rows, n_cols = calculate_grid_layout(len(plot_specs))
    print(f"\nGrid Layout: {n_rows} rows × {n_cols} columns = {n_rows * n_cols} total positions")
    print(f"Used positions: {len(plot_specs)}")

def main():
    """
    Main function to create the grid comparison plot.
    """
    # Parse command-line arguments
    args = parse_arguments()
    log_filename = args.log_file
    output_dir = args.output_dir

    print("TC Flash PQ Log Grid Comparison Plotter")
    print("="*50)

    # Validate log file
    validate_log_file(log_filename)

    print(f"Parsing log file: {log_filename}")

    # Parse the log file
    try:
        config_data, dm_data = parse_log_file(log_filename)
    except Exception as e:
        print(f"Error parsing log file: {e}")
        print("Please ensure the log file is in the correct TC Flash PQ format.")
        sys.exit(1)

    # Verify we got data
    if not config_data and not dm_data:
        print("Error: No parameter data found in log file!")
        print("Please check that the log file contains Config and DM value sections.")
        sys.exit(1)

    # Print summary of what will be plotted
    print_grid_summary(config_data, dm_data)

    # Create the grid comparison plot
    try:
        n_plots = create_grid_comparison_plot(config_data, dm_data, output_dir)
    except Exception as e:
        print(f"Error creating plots: {e}")
        sys.exit(1)

    print(f"\nGrid comparison plotting complete!")
    print(f"Created comprehensive visualization with {n_plots} parameter plots.")
    print(f"Output file: {os.path.join(output_dir, 'all_parameters_comparison_grid.png')}")


if __name__ == "__main__":
    main()
