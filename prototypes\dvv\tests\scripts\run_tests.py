#!/usr/bin/env python3
"""
Test runner for PQ Tuning Config Editor
Author: <PERSON> <<EMAIL>>

This script runs all tests for the PQ tuning config editor web application.
It provides options to run individual test suites or all tests together.
"""

import sys
import subprocess
import argparse
from pathlib import Path
import time

def run_test_file(test_file, verbose=False):
    """Run a specific test file and return results"""
    print(f"\n{'='*60}")
    print(f"Running {test_file}")
    print(f"{'='*60}")

    start_time = time.time()

    try:
        # Use pytest to run the test file to handle imports correctly
        cmd = [sys.executable, "-m", "pytest", str(test_file)]
        if verbose:
            cmd.append("-v")
        else:
            cmd.append("-q")

        result = subprocess.run(
            cmd,
            cwd=Path(__file__).parent,
            capture_output=not verbose,
            text=True,
            timeout=60  # 60 second timeout
        )

        end_time = time.time()
        duration = end_time - start_time

        if result.returncode == 0:
            print(f"[OK] {test_file.name} PASSED ({duration:.2f}s)")
            if verbose and result.stdout:
                print(result.stdout)
            return True, duration
        else:
            print(f"[FAIL] {test_file.name} FAILED ({duration:.2f}s)")
            if result.stdout:
                print("STDOUT:")
                print(result.stdout)
            if result.stderr:
                print("STDERR:")
                print(result.stderr)
            return False, duration
            
    except subprocess.TimeoutExpired:
        print(f"[FAIL] {test_file.name} TIMEOUT (>60s)")
        return False, 60.0
    except Exception as e:
        print(f"[FAIL] {test_file.name} ERROR: {e}")
        return False, 0.0

def run_all_tests(verbose=False):
    """Run all test files"""
    tests_dir = Path(__file__).parent.parent

    # Define test files in order of execution
    test_files = [
        # Unit tests
        tests_dir / "unit/core/test_config.py",
        tests_dir / "unit/core/test_exceptions.py",
        tests_dir / "unit/core/test_utils.py",
        tests_dir / "unit/core/test_upload_directory.py",
        tests_dir / "unit/processors/test_log_processors.py",
        tests_dir / "unit/web/test_registry.py",
        tests_dir / "unit/web/test_api.py",
        tests_dir / "unit/web/test_session_manager.py",
        # Integration tests
        tests_dir / "integration/test_web_app_selection.py",
        tests_dir / "integration/test_workflow.py",
        tests_dir / "integration/test_subprocess_architecture.py",
        tests_dir / "integration/test_download.py",
        # UI tests
        tests_dir / "ui/test_firmware_log_workflow.py",
        tests_dir / "ui/test_grid_comparison_ui.py",
        tests_dir / "ui/test_log_info_visibility.py",
        # Script tests
        tests_dir / "scripts/test_unified_download.py"
    ]
    
    # Check that all test files exist
    missing_files = [f for f in test_files if not f.exists()]
    if missing_files:
        print("Error: Missing test files:")
        for f in missing_files:
            print(f"  - {f}")
        return False
    
    print("PQ Tuning Config Editor - Test Suite")
    print(f"Running {len(test_files)} test files...")
    
    results = []
    total_duration = 0
    
    for test_file in test_files:
        success, duration = run_test_file(test_file, verbose)
        results.append((test_file.name, success, duration))
        total_duration += duration
        
        if not success:
            print(f"\n[WARN] Test {test_file.name} failed. Continuing with remaining tests...")
    
    # Print summary
    print(f"\n{'='*60}")
    print("TEST SUMMARY")
    print(f"{'='*60}")
    
    passed = sum(1 for _, success, _ in results if success)
    failed = len(results) - passed
    
    for test_name, success, duration in results:
        status = "PASS" if success else "FAIL"
        print(f"{status:4} | {test_name:25} | {duration:6.2f}s")
    
    print(f"{'='*60}")
    print(f"Total: {len(results)} tests | Passed: {passed} | Failed: {failed}")
    print(f"Total Duration: {total_duration:.2f}s")
    
    if failed == 0:
        print("[SUCCESS] All tests passed!")
        return True
    else:
        print(f"[ERROR] {failed} test(s) failed!")
        return False

def run_specific_test(test_name, verbose=False):
    """Run a specific test file"""
    tests_dir = Path(__file__).parent.parent

    # Search for test file in all subdirectories
    test_file = None
    for pattern in [f"unit/**/test_{test_name}.py", f"integration/test_{test_name}.py", f"ui/test_{test_name}.py"]:
        matches = list(tests_dir.glob(pattern))
        if matches:
            test_file = matches[0]
            break

    if not test_file or not test_file.exists():
        print(f"Error: Test file test_{test_name}.py does not exist")
        available_tests = []
        for pattern in ["unit/**/test_*.py", "integration/test_*.py", "ui/test_*.py"]:
            available_tests.extend([f.stem.replace('test_', '') for f in tests_dir.glob(pattern)])
        print(f"Available tests: {', '.join(sorted(set(available_tests)))}")
        return False
    
    success, duration = run_test_file(test_file, verbose)
    return success

def main():
    """Main test runner function"""
    parser = argparse.ArgumentParser(
        description="Test runner for Web Application Selection Feature",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_tests.py                    # Run all tests
  python run_tests.py --test registry    # Run only registry tests
  python run_tests.py --test api         # Run only API tests
  python run_tests.py --verbose          # Run all tests with verbose output
  python run_tests.py --list             # List available tests
        """
    )
    
    parser.add_argument(
        '--test', '-t',
        help='Run specific test (registry, api, web_app_selection, workflow)'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Show verbose output from tests'
    )
    
    parser.add_argument(
        '--list', '-l',
        action='store_true',
        help='List available tests'
    )
    
    args = parser.parse_args()
    
    if args.list:
        tests_dir = Path(__file__).parent.parent
        available_tests = []
        for pattern in ["unit/**/test_*.py", "integration/test_*.py", "ui/test_*.py"]:
            available_tests.extend([f.stem.replace('test_', '') for f in tests_dir.glob(pattern)])
        print("Available tests:")
        for test in sorted(set(available_tests)):
            print(f"  - {test}")
        return
    
    if args.test:
        success = run_specific_test(args.test, args.verbose)
    else:
        success = run_all_tests(args.verbose)
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
