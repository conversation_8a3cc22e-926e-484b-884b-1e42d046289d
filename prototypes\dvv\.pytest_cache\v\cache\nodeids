["tests/integration/test_subprocess_architecture.py::TestFirmwareLogDetector::test_invalid_firmware_log_integration", "tests/integration/test_subprocess_architecture.py::TestFirmwareLogDetector::test_nonexistent_firmware_log", "tests/integration/test_subprocess_architecture.py::TestFirmwareLogDetector::test_tc_flash_log_detected_as_firmware_log", "tests/integration/test_subprocess_architecture.py::TestFirmwareLogDetector::test_valid_firmware_log_integration", "tests/integration/test_subprocess_architecture.py::TestLogProcessorManager::test_process_with_user_selection_firmware_log_visualizer", "tests/integration/test_subprocess_architecture.py::TestLogProcessorManager::test_process_with_user_selection_grid_comparison", "tests/integration/test_subprocess_architecture.py::TestSubprocessLogProcessor::test_execute_firmware_log_visualizer", "tests/integration/test_subprocess_architecture.py::TestSubprocessLogProcessor::test_execute_grid_comparison_plotter", "tests/integration/test_subprocess_architecture.py::TestSubprocessLogProcessor::test_get_bin_directory", "tests/integration/test_subprocess_architecture.py::TestTCFlashLogDetector::test_invalid_log_file", "tests/integration/test_subprocess_architecture.py::TestTCFlashLogDetector::test_nonexistent_file", "tests/integration/test_subprocess_architecture.py::TestTCFlashLogDetector::test_valid_tc_flash_log", "tests/integration/test_subprocess_architecture.py::TestWebApplicationRegistry::test_application_properties", "tests/integration/test_subprocess_architecture.py::TestWebApplicationRegistry::test_get_enabled_applications", "tests/integration/test_web_app_selection.py::test_config", "tests/integration/test_web_app_selection.py::test_json_serialization", "tests/integration/test_web_app_selection.py::test_web_app_registry", "tests/integration/test_workflow.py::test_complete_workflow", "tests/integration/test_workflow.py::test_english_only_ui", "tests/integration/test_workflow.py::test_persistence_simulation", "tests/scripts/test_unified_download.py::test_unified_download_functionality", "tests/ui/test_firmware_log_workflow.py::TestFirmwareLogWorkflow::test_firmware_log_routing", "tests/ui/test_firmware_log_workflow.py::TestFirmwareLogWorkflow::test_grid_comparison_tc_flash_validation", "tests/ui/test_firmware_log_workflow.py::TestFirmwareLogWorkflow::test_tc_flash_log_with_firmware_visualizer", "tests/ui/test_log_info_visibility.py::TestLogInfoVisibility::test_backward_compatibility", "tests/ui/test_log_info_visibility.py::TestLogInfoVisibility::test_comment_accuracy", "tests/ui/test_log_info_visibility.py::TestLogInfoVisibility::test_consistency_with_main_logic", "tests/ui/test_log_info_visibility.py::TestLogInfoVisibility::test_javascript_condition_logic", "tests/ui/test_log_info_visibility.py::TestLogInfoVisibility::test_maintainability_improvement", "tests/ui/test_log_info_visibility.py::TestLogInfoVisibility::test_processing_mode_logic", "tests/ui/test_log_info_visibility.py::TestLogInfoVisibility::test_processor_exclusion_logic", "tests/ui/test_log_info_visibility.py::TestLogInfoVisibility::test_template_consistency", "tests/ui/test_log_info_visibility.py::TestLogInfoVisibility::test_ui_behavior_test_file_updated", "tests/ui/test_processing_mode_refactor.py::TestProcessingModeRefactor::test_comment_accuracy", "tests/ui/test_processing_mode_refactor.py::TestProcessingModeRefactor::test_javascript_refactoring_complete", "tests/ui/test_processing_mode_refactor.py::TestProcessingModeRefactor::test_maintainability_scenarios", "tests/ui/test_processing_mode_refactor.py::TestProcessingModeRefactor::test_processing_mode_values_consistency", "tests/ui/test_processing_mode_refactor.py::TestProcessingModeRefactor::test_ui_test_file_updated", "tests/unit/core/test_config.py::TestConfiguration::test_logging_configuration", "tests/unit/core/test_config.py::TestConfiguration::test_upload_configuration", "tests/unit/core/test_config.py::TestConfiguration::test_web_applications_config", "tests/unit/core/test_exceptions.py::TestExceptions::test_custom_exceptions_exist", "tests/unit/core/test_exceptions.py::TestExceptions::test_exceptions_module_import", "tests/unit/core/test_upload_directory.py::test_command_line_integration", "tests/unit/core/test_upload_directory.py::test_config_environment_variable", "tests/unit/core/test_upload_directory.py::test_validate_upload_directory_creation", "tests/unit/core/test_upload_directory.py::test_validate_upload_directory_existing", "tests/unit/core/test_upload_directory.py::test_validate_upload_directory_invalid_parent", "tests/unit/core/test_utils.py::TestUtilities::test_setup_logging", "tests/unit/core/test_utils.py::TestUtilities::test_utility_functions_exist", "tests/unit/processors/test_log_processors.py::TestFirmwareLogDetector::test_get_log_info", "tests/unit/processors/test_log_processors.py::TestFirmwareLogDetector::test_is_firmware_log_nonexistent_file", "tests/unit/processors/test_log_processors.py::TestFirmwareLogDetector::test_is_firmware_log_with_general_parameters", "tests/unit/processors/test_log_processors.py::TestFirmwareLogDetector::test_is_firmware_log_with_invalid_content", "tests/unit/processors/test_log_processors.py::TestFirmwareLogDetector::test_is_firmware_log_with_structured_content", "tests/unit/processors/test_log_processors.py::TestFirmwareLogDetector::test_is_firmware_log_with_valid_dolby_content", "tests/unit/processors/test_log_processors.py::TestFirmwareLogDetector::test_is_firmware_log_with_valid_level_content", "tests/unit/processors/test_log_processors.py::TestLogProcessingResult::test_log_processing_result_creation", "tests/unit/processors/test_log_processors.py::TestLogProcessorManager::test_process_log_file_with_grid_comparison", "tests/unit/processors/test_log_processors.py::TestLogProcessorManager::test_process_log_file_with_unknown_processor", "tests/unit/processors/test_log_processors.py::TestSubprocessLogProcessor::test_execute_grid_comparison_plotter", "tests/unit/processors/test_log_processors.py::TestTCFlashLogDetector::test_get_log_info", "tests/unit/processors/test_log_processors.py::TestTCFlashLogDetector::test_is_tc_flash_log_with_invalid_content", "tests/unit/processors/test_log_processors.py::TestTCFlashLogDetector::test_is_tc_flash_log_with_valid_content", "tests/unit/web/test_api.py::test_session_app_tracking", "tests/unit/web/test_api.py::test_session_creation", "tests/unit/web/test_api.py::test_session_retrieval", "tests/unit/web/test_api.py::test_session_validation", "tests/unit/web/test_api.py::test_url_generation_for_apps", "tests/unit/web/test_api.py::test_user_registration_logic", "tests/unit/web/test_api.py::test_web_applications_api_data", "tests/unit/web/test_registry.py::test_enable_disable_application", "tests/unit/web/test_registry.py::test_get_all_applications", "tests/unit/web/test_registry.py::test_get_application", "tests/unit/web/test_registry.py::test_get_application_url", "tests/unit/web/test_registry.py::test_get_enabled_applications", "tests/unit/web/test_registry.py::test_is_valid_application", "tests/unit/web/test_registry.py::test_new_bin_applications", "tests/unit/web/test_registry.py::test_real_config_applications", "tests/unit/web/test_registry.py::test_register_application", "tests/unit/web/test_registry.py::test_registry_initialization", "tests/unit/web/test_session_manager.py::TestSessionManager::test_create_session", "tests/unit/web/test_session_manager.py::TestSessionManager::test_get_nonexistent_session", "tests/unit/web/test_session_manager.py::TestSessionManager::test_get_session", "tests/unit/web/test_session_manager.py::TestSessionManager::test_session_data_structure", "tests/unit/web/test_session_manager.py::TestSessionManager::test_session_manager_creation", "tests/unit/web/test_session_manager.py::TestSessionManager::test_session_persistence"]