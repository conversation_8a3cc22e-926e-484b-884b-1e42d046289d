# PQ Config Validator Docker Ignore File
# Author: <PERSON>
# Excludes files and directories from Docker build context

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
.venv/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Git
.git/
.gitignore
.gitattributes

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Documentation
README.md
*.md
docs/
documentation/

# Test files
test_*.py
*_test.py
tests/
testing/
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/

# Logs and temporary files
*.log
logs/
temp/
tmp/
.tmp/

# Data directories (will be mounted as volumes)
data/
uploads/
sessions/

# Development and deployment scripts
deploy.sh
deploy.bat
run_dev.py
dev_*.py

# Environment files
.env.local
.env.development
.env.test
.env.production

# Backup files
*.bak
*.backup
*.old

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node.js (if any frontend build tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Application specific
test_report.*
verify_*.py
test_*.py
