#!/usr/bin/env python3
"""
Log Processing Integration for TC Flash PQ Log Analysis

This module provides integration functionality for the web application to execute
the original standalone grid_comparison_plotter.py and log_parser_plotter.py scripts
as subprocess commands while capturing their output for web display.
"""

import subprocess
import os
import sys
import re
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import logging
from dataclasses import dataclass
import traceback
import shutil
from collections import defaultdict

# Configure logging
logger = logging.getLogger(__name__)

@dataclass
class LogProcessingResult:
    """Result of log processing operation"""
    success: bool
    message: str
    processor_used: str
    output_files: List[str] = None
    static_files: List[Dict[str, str]] = None  # List of {filename, url} dicts for web display
    error_details: Optional[str] = None
    command_output: Optional[str] = None

    def __post_init__(self):
        if self.output_files is None:
            self.output_files = []
        if self.static_files is None:
            self.static_files = []

class FirmwareLogDetector:
    """Detects and validates firmware log files for firmware_log_visualizer"""

    @staticmethod
    def is_firmware_log(file_path: Path) -> bool:
        """
        Check if a file is a valid firmware log file that can be processed by firmware_log_visualizer.

        Args:
            file_path: Path to the file to check

        Returns:
            bool: True if file appears to be a firmware log with extractable parameters
        """
        try:
            # Validate file exists and is readable
            if not file_path.exists():
                logger.warning(f"File does not exist: {file_path}")
                return False

            if not file_path.is_file():
                logger.warning(f"Path is not a file: {file_path}")
                return False

            # Check file size (avoid processing extremely large files)
            file_size = file_path.stat().st_size
            if file_size > 100 * 1024 * 1024:  # 100MB limit
                logger.warning(f"File too large for firmware log processing: {file_size} bytes")
                return False

            if file_size == 0:
                logger.warning(f"File is empty: {file_path}")
                return False

            with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
                content = file.read()

            # Check for firmware log parameter patterns
            # These patterns are based on what firmware_log_visualizer expects to extract
            firmware_patterns = [
                # Dolby debug format (parentheses)
                r'dBrightness\(\d+\)',
                r'dContrast\(\d+\)',
                r'dSaturation\(\d+\)',
                r'dBacklight\(\d+\)',
                r'dLocalContrast\(\d+\)',
                r'gainPos_\w+\(\s*\d+\)',
                r'gainNeg_\w+\(\s*\d+\)',
                r'confidence\(\s*\d+\)',
                r'precisionRenderingStrength\(\s*\d+\)',

                # Level parameters (colon format)
                r'\w+_boost:\s+\d+',
                r'\w+_stretch:\s+\d+',
                r'\w+_drop:\s+\d+',
                r'\w+_indicator:\s+\d+',
                r'\w+_lift:\s+\d+',

                # Mapping strength (equals format)
                r'UpMappingStrength=\s*\d+',

                # Structured format patterns (like TC Flash but also firmware logs)
                r'intensity_level:\s+\d+',
                r'gain_\w+:\s+\d+',
                r'LocalMappingStrength\s+\d+',
                r'TMax\s+\d+',
            ]

            # Count matching patterns
            pattern_matches = 0
            for pattern in firmware_patterns:
                if re.search(pattern, content, re.MULTILINE | re.IGNORECASE):
                    pattern_matches += 1

            # Require at least 3 pattern matches for confidence
            # This is more lenient than TC Flash logs since firmware logs can have various formats
            if pattern_matches >= 3:
                logger.debug(f"Firmware log detected with {pattern_matches} pattern matches: {file_path}")
                return True

            # Additional check: if it's a text file with numerical parameters, it might be processable
            # Look for any parameter-like patterns with numbers
            general_param_patterns = [
                r'\w+\(\d+\)',  # parameter(value)
                r'\w+:\s*\d+',  # parameter: value
                r'\w+=\s*\d+',  # parameter=value
                r'\w+\s+\d+',   # parameter value
            ]

            general_matches = 0
            for pattern in general_param_patterns:
                matches = re.findall(pattern, content, re.MULTILINE | re.IGNORECASE)
                general_matches += len(matches)

            # If we have many general parameter patterns, it's likely a firmware log
            if general_matches >= 10:
                logger.debug(f"Firmware log detected with {general_matches} general parameter matches: {file_path}")
                return True

            logger.debug(f"File does not appear to be a firmware log: {file_path} (patterns: {pattern_matches}, general: {general_matches})")
            return False

        except UnicodeDecodeError as e:
            logger.warning(f"File encoding issue in {file_path}: {e}")
            return False
        except PermissionError as e:
            logger.error(f"Permission denied reading {file_path}: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error checking firmware log file {file_path}: {e}")
            logger.debug(f"Traceback: {traceback.format_exc()}")
            return False

    @staticmethod
    def get_log_info(file_path: Path) -> Dict[str, Any]:
        """
        Get basic information about the firmware log file by analyzing its content.

        Returns:
            dict: Information about the log file including parameter counts, file size, etc.
        """
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
                content = file.read()

            # Count different types of parameters
            dolby_params = set()
            level_params = set()
            structured_params = set()
            general_params = set()

            # Dolby debug format parameters
            dolby_patterns = [
                r'(dBrightness|dContrast|dSaturation|dBacklight|dLocalContrast)\(\d+\)',
                r'(gainPos_\w+|gainNeg_\w+)\(\s*\d+\)',
                r'(confidence|precisionRenderingStrength)\(\s*\d+\)',
            ]
            for pattern in dolby_patterns:
                matches = re.findall(pattern, content, re.MULTILINE | re.IGNORECASE)
                dolby_params.update([match if isinstance(match, str) else match[0] for match in matches])

            # Level parameters (colon format)
            level_matches = re.findall(r'(\w+(?:_boost|_stretch|_drop|_indicator|_lift)):\s+\d+', content, re.MULTILINE | re.IGNORECASE)
            level_params.update(level_matches)

            # Structured format parameters
            structured_matches = re.findall(r'(intensity_level|gain_\w+|LocalMappingStrength|TMax)(?::\s+|\s+)\d+', content, re.MULTILINE | re.IGNORECASE)
            structured_params.update(structured_matches)

            # General parameter patterns
            general_matches = re.findall(r'(\w+)(?:\(|\s*[:=]\s*|\s+)\d+', content, re.MULTILINE | re.IGNORECASE)
            general_params.update(general_matches)

            # Estimate total measurements by counting all numerical values in parameter contexts
            total_measurements = len(re.findall(r'(?:\w+(?:\(|:\s*|=\s*|\s+))\d+', content, re.MULTILINE | re.IGNORECASE))

            return {
                "dolby_parameters": len(dolby_params),
                "level_parameters": len(level_params),
                "structured_parameters": len(structured_params),
                "total_unique_parameters": len(dolby_params | level_params | structured_params),
                "total_measurements": total_measurements,
                "file_size_bytes": file_path.stat().st_size,
                "estimated_processable": total_measurements > 0
            }

        except Exception as e:
            logger.error(f"Error getting firmware log info for {file_path}: {e}")
            return {
                "dolby_parameters": 0,
                "level_parameters": 0,
                "structured_parameters": 0,
                "total_unique_parameters": 0,
                "total_measurements": 0,
                "file_size_bytes": 0,
                "estimated_processable": False
            }


class TCFlashLogDetector:
    """Detects and validates TC Flash PQ log files"""

    @staticmethod
    def is_tc_flash_log(file_path: Path) -> bool:
        """
        Check if a file is a valid TC Flash PQ log file.

        Args:
            file_path: Path to the file to check

        Returns:
            bool: True if file appears to be a TC Flash PQ log
        """
        try:
            # Validate file exists and is readable
            if not file_path.exists():
                logger.warning(f"File does not exist: {file_path}")
                return False

            if not file_path.is_file():
                logger.warning(f"Path is not a file: {file_path}")
                return False

            # Check file size (avoid processing extremely large files)
            file_size = file_path.stat().st_size
            if file_size > 100 * 1024 * 1024:  # 100MB limit
                logger.warning(f"File too large for log processing: {file_size} bytes")
                return False

            if file_size == 0:
                logger.warning(f"File is empty: {file_path}")
                return False

            with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
                content = file.read()

            # Check for required sections
            has_config = "Config values:" in content
            has_dm = "DM values:" in content
            has_fallback = "fallback: 0" in content

            if not (has_config and has_dm and has_fallback):
                logger.debug(f"Missing required sections in {file_path}: config={has_config}, dm={has_dm}, fallback={has_fallback}")
                return False

            return True

        except UnicodeDecodeError as e:
            logger.warning(f"File encoding issue in {file_path}: {e}")
            return False
        except PermissionError as e:
            logger.error(f"Permission denied reading {file_path}: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error checking TC Flash log file {file_path}: {e}")
            logger.debug(f"Traceback: {traceback.format_exc()}")
            return False

    @staticmethod
    def get_log_info(file_path: Path) -> Dict[str, Any]:
        """
        Get basic information about the TC Flash log file by parsing it.

        Returns:
            dict: Information about the log file including frame count, parameter counts, etc.
        """
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
                content = file.read()

            # Count frames by counting "fallback: 0" occurrences
            frame_count = content.count("fallback: 0")

            # Count approximate parameters by looking for patterns
            config_section = False
            dm_section = False
            config_params = set()
            dm_params = set()

            for line in content.split('\n'):
                line = line.strip()

                if line == "Config values:":
                    config_section = True
                    dm_section = False
                    continue
                elif line == "DM values:":
                    config_section = False
                    dm_section = True
                    continue
                elif line == "fallback: 0":
                    config_section = False
                    dm_section = False
                    continue

                if config_section and line and not line.startswith("fallback"):
                    # Config format: parameter: value
                    match = re.match(r'\s*([^:]+):\s*\d+', line)
                    if match:
                        config_params.add(match.group(1).strip())

                if dm_section and line and not line.startswith("fallback"):
                    # DM format: parameter value
                    match = re.match(r'\s*(\w+)\s+\d+', line)
                    if match:
                        dm_params.add(match.group(1).strip())

            return {
                "total_frames": frame_count,
                "config_parameters": len(config_params),
                "dm_parameters": len(dm_params),
                "total_parameters": len(config_params) + len(dm_params)
            }

        except Exception as e:
            logger.error(f"Error getting log info for {file_path}: {e}")
            return {
                "total_frames": 0,
                "config_parameters": 0,
                "dm_parameters": 0,
                "total_parameters": 0
            }

class SubprocessLogProcessor:
    """Executes original log processing scripts as subprocesses"""

    @staticmethod
    def _copy_png_files_to_static(output_dir: Path, png_files: List[str], app_type: str) -> List[Dict[str, str]]:
        """
        Copy PNG files to static directory for web access

        Args:
            output_dir: Directory containing the PNG files
            png_files: List of PNG filenames to copy
            app_type: Application type (grid_comparison or log_parser)

        Returns:
            List of dicts with filename and URL for each copied file
        """
        static_files = []

        try:
            # Get static directory path (relative to current module)
            current_dir = Path(__file__).parent
            static_dir = current_dir / "static" / "images" / app_type
            static_dir.mkdir(parents=True, exist_ok=True)

            for png_file in png_files:
                src_path = output_dir / png_file
                if src_path.exists():
                    # Create unique filename with timestamp to avoid conflicts
                    import time
                    timestamp = int(time.time())
                    unique_filename = f"{timestamp}_{png_file}"
                    dest_path = static_dir / unique_filename

                    # Copy file to static directory
                    shutil.copy2(src_path, dest_path)

                    # Create URL for web access
                    static_url = f"/static/images/{app_type}/{unique_filename}"

                    static_files.append({
                        "filename": png_file,
                        "unique_filename": unique_filename,
                        "url": static_url
                    })

                    logger.info(f"Copied {png_file} to static directory: {static_url}")
                else:
                    logger.warning(f"PNG file not found for copying: {src_path}")

        except Exception as e:
            logger.error(f"Failed to copy PNG files to static directory: {e}")

        return static_files

    @staticmethod
    def get_bin_directory() -> Path:
        """Get the bin directory containing the original scripts"""
        # Assume bin directory is relative to the current module
        current_dir = Path(__file__).parent
        bin_dir = current_dir / "bin"

        if not bin_dir.exists():
            # Try parent directory
            bin_dir = current_dir.parent / "bin"

        return bin_dir

    @staticmethod
    def execute_grid_comparison_plotter(input_file: Path, output_dir: Path) -> LogProcessingResult:
        """
        Execute grid_comparison_plotter.py as subprocess

        Args:
            input_file: Path to the input log file
            output_dir: Directory to save the generated plot

        Returns:
            LogProcessingResult: Result of the processing operation
        """
        try:
            # Ensure output directory exists
            output_dir.mkdir(parents=True, exist_ok=True)

            # Get the script path
            bin_dir = SubprocessLogProcessor.get_bin_directory()
            script_path = bin_dir / "grid_comparison_plotter.py"

            if not script_path.exists():
                return LogProcessingResult(
                    success=False,
                    message=f"Grid comparison plotter script not found: {script_path}",
                    processor_used="grid_comparison",
                    error_details=f"Script path: {script_path}"
                )

            # Prepare command with correct argument format
            # grid_comparison_plotter.py expects: [log_file] [--output-dir output_dir]
            cmd = [
                sys.executable,  # Use current Python interpreter
                str(script_path),
                str(input_file),
                "--output-dir",
                str(output_dir)
            ]

            logger.info(f"Executing command: {' '.join(cmd)}")

            # Execute the subprocess
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300,  # 5 minute timeout
                cwd=str(bin_dir)  # Set working directory to bin
            )

            # Check if the process succeeded
            if result.returncode != 0:
                error_msg = f"Grid comparison plotter failed with return code {result.returncode}"
                logger.error(f"{error_msg}. stderr: {result.stderr}")
                return LogProcessingResult(
                    success=False,
                    message=error_msg,
                    processor_used="grid_comparison",
                    error_details=result.stderr,
                    command_output=result.stdout
                )

            # Check for expected output files and copy to static directory
            expected_output = output_dir / "all_parameters_comparison_grid.png"
            if not expected_output.exists():
                return LogProcessingResult(
                    success=False,
                    message="Grid comparison plot was not generated",
                    processor_used="grid_comparison",
                    error_details="Expected output file not found",
                    command_output=result.stdout
                )

            # Copy PNG files to static directory for web access
            static_files = SubprocessLogProcessor._copy_png_files_to_static(
                output_dir,
                ["all_parameters_comparison_grid.png"],
                "grid_comparison"
            )

            return LogProcessingResult(
                success=True,
                message="Successfully generated grid comparison plot",
                processor_used="grid_comparison",
                output_files=["all_parameters_comparison_grid.png"],
                static_files=static_files,
                command_output=result.stdout
            )

        except subprocess.TimeoutExpired:
            logger.error("Grid comparison plotter timed out")
            return LogProcessingResult(
                success=False,
                message="Grid comparison plotter timed out (exceeded 5 minutes)",
                processor_used="grid_comparison",
                error_details="Process timeout"
            )
        except Exception as e:
            logger.error(f"Error executing grid comparison plotter: {e}")
            logger.debug(f"Traceback: {traceback.format_exc()}")
            return LogProcessingResult(
                success=False,
                message=f"Failed to execute grid comparison plotter: {str(e)}",
                processor_used="grid_comparison",
                error_details=str(e)
            )

    @staticmethod
    def execute_firmware_log_visualizer(input_file: Path, output_dir: Path) -> LogProcessingResult:
        """
        Execute firmware_log_visualizer.py as subprocess

        Args:
            input_file: Path to the input firmware log file
            output_dir: Directory to save the generated plots and CSV

        Returns:
            LogProcessingResult: Result of the processing operation
        """
        try:
            # Ensure output directory exists
            output_dir.mkdir(parents=True, exist_ok=True)

            # Get the script path
            bin_dir = SubprocessLogProcessor.get_bin_directory()
            script_path = bin_dir / "firmware_log_visualizer.py"

            if not script_path.exists():
                return LogProcessingResult(
                    success=False,
                    message=f"Firmware log visualizer script not found: {script_path}",
                    processor_used="firmware_log_visualizer",
                    error_details=f"Script path: {script_path}"
                )

            # Prepare command with correct argument format
            # firmware_log_visualizer.py expects: [log_file] --output [output_dir] --no-show
            cmd = [
                sys.executable,  # Use current Python interpreter
                str(script_path),
                str(input_file),
                "--output",
                str(output_dir),
                "--no-show"  # Prevent GUI display in web context
            ]

            logger.info(f"Executing command: {' '.join(cmd)}")

            # Execute the subprocess
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300,  # 5 minute timeout
                cwd=str(bin_dir)  # Set working directory to bin
            )

            if result.returncode != 0:
                return LogProcessingResult(
                    success=False,
                    message=f"Firmware log visualizer failed with return code {result.returncode}",
                    processor_used="firmware_log_visualizer",
                    error_details=result.stderr,
                    command_output=result.stdout
                )

            # Look for generated files in output directory
            output_files = []

            # Expected outputs: summary.csv and [input_filename_stem].png
            input_stem = input_file.stem
            expected_png = output_dir / f"{input_stem}.png"
            expected_csv = output_dir / "summary.csv"

            if expected_png.exists():
                output_files.append(expected_png.name)
            if expected_csv.exists():
                output_files.append(expected_csv.name)

            if not output_files:
                return LogProcessingResult(
                    success=False,
                    message="No output files were generated by firmware log visualizer",
                    processor_used="firmware_log_visualizer",
                    error_details="Expected PNG and CSV files not found",
                    command_output=result.stdout
                )

            # Copy PNG files to static directory for web access (only PNG files, not CSV)
            png_files = [f for f in output_files if f.endswith('.png')]
            static_files = SubprocessLogProcessor._copy_png_files_to_static(
                output_dir,
                png_files,
                "firmware_log_visualizer"
            )

            return LogProcessingResult(
                success=True,
                message=f"Firmware log visualization generated successfully. Created {len(output_files)} files.",
                processor_used="firmware_log_visualizer",
                output_files=output_files,
                static_files=static_files,
                command_output=result.stdout
            )

        except subprocess.TimeoutExpired:
            return LogProcessingResult(
                success=False,
                message="Firmware log visualizer timed out after 5 minutes",
                processor_used="firmware_log_visualizer",
                error_details="Process exceeded maximum execution time"
            )
        except Exception as e:
            logger.error(f"Error executing firmware log visualizer: {e}")
            return LogProcessingResult(
                success=False,
                message=f"Error executing firmware log visualizer: {str(e)}",
                processor_used="firmware_log_visualizer",
                error_details=str(e)
            )

class LogProcessorManager:
    """Manages log processing operations using user-selected processors"""

    @staticmethod
    def process_log_file(file_path: Path, output_dir: Path, processor_type: str) -> LogProcessingResult:
        """
        Process a log file using the specified processor.

        Args:
            file_path: Path to the input log file
            output_dir: Directory to save generated plots
            processor_type: Specific processor to use ('grid_comparison' or 'firmware_log_visualizer')

        Returns:
            LogProcessingResult: Result of the processing operation
        """
        # Validate input file based on processor type
        # TC Flash log validation is only required for grid_comparison
        if processor_type == "grid_comparison":
            if not TCFlashLogDetector.is_tc_flash_log(file_path):
                return LogProcessingResult(
                    success=False,
                    message="File is not a valid TC Flash PQ log file",
                    processor_used="none",
                    error_details="Missing required sections: Config values, DM values, or fallback markers"
                )
        # For firmware_log_visualizer, validate it's a processable firmware log
        elif processor_type == "firmware_log_visualizer":
            if not FirmwareLogDetector.is_firmware_log(file_path):
                return LogProcessingResult(
                    success=False,
                    message="File is not a valid firmware log file with extractable parameters",
                    processor_used="none",
                    error_details="File does not contain recognizable firmware log parameter patterns that can be processed by firmware_log_visualizer"
                )

        # Validate processor type
        if not processor_type:
            return LogProcessingResult(
                success=False,
                message="No processor type specified",
                processor_used="none",
                error_details="Processor type must be specified from web interface selection"
            )

        # Process with the selected processor
        if processor_type == "grid_comparison":
            return SubprocessLogProcessor.execute_grid_comparison_plotter(file_path, output_dir)
        elif processor_type == "firmware_log_visualizer":
            return SubprocessLogProcessor.execute_firmware_log_visualizer(file_path, output_dir)
        else:
            return LogProcessingResult(
                success=False,
                message=f"Unknown processor type: {processor_type}",
                processor_used="none",
                error_details=f"Valid processors are: grid_comparison, firmware_log_visualizer"
            )
