#!/usr/bin/env python3
"""
Log Parser and <PERSON><PERSON><PERSON> for TC Flash PQ Log Analysis

This script parses the TC_Flash_first_few_frames_pq_log.txt file and creates
line plots for each parameter showing how they change over time/frames.
"""

import re
import matplotlib.pyplot as plt
import numpy as np
from collections import defaultdict
import os

def parse_log_file(filename):
    """
    Parse the log file and extract parameter values for each frame.
    
    Returns:
        tuple: (config_data, dm_data) where each is a dict of parameter_name -> list of values
    """
    config_data = defaultdict(list)
    dm_data = defaultdict(list)
    
    with open(filename, 'r') as file:
        lines = file.readlines()
    
    current_section = None
    frame_count = 0
    
    for line in lines:
        line = line.strip()
        
        # Detect start of new frame
        if line == "fallback: 0":
            frame_count += 1
            current_section = None
            continue
        
        # Detect section headers
        if line == "Config values:":
            current_section = "config"
            continue
        elif line == "DM values:":
            current_section = "dm"
            continue
        
        # Parse parameter lines
        if current_section and line and not line.startswith("fallback"):
            # Match patterns like:
            # "  intensity_level:                         10"
            # "  gain_PrecisionRenderingStrength:        127 (scale  7)"
            # "  LmOn                                      1 (scale  0)"  (DM values format)

            # Try Config values format first (with colon)
            match = re.match(r'\s*([^:]+):\s*(\d+)(?:\s*\(scale\s*\d+\))?', line)
            if not match:
                # Try DM values format (without colon, just spaces)
                match = re.match(r'\s*(\w+)\s+(\d+)(?:\s*\(scale\s*\d+\))?', line)

            if match:
                param_name = match.group(1).strip()
                value = int(match.group(2))

                # Skip empty parameter names
                if not param_name:
                    continue

                if current_section == "config":
                    config_data[param_name].append(value)
                elif current_section == "dm":
                    dm_data[param_name].append(value)
    
    return config_data, dm_data

def create_plots(config_data, dm_data, output_dir="plots"):
    """
    Create line plots for each parameter and save them as PNG files.
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Set up matplotlib style
    plt.style.use('default')
    plt.rcParams['figure.figsize'] = (12, 8)
    plt.rcParams['font.size'] = 10
    
    # Plot Config values
    if config_data:
        print("Creating plots for Config values...")
        create_parameter_plots(config_data, "Config Values", output_dir, "config")
    
    # Plot DM values
    if dm_data:
        print("Creating plots for DM values...")
        create_parameter_plots(dm_data, "DM Values", output_dir, "dm")
    
    # Create combined overview plots
    create_overview_plots(config_data, dm_data, output_dir)

def create_parameter_plots(data, section_name, output_dir, prefix):
    """
    Create individual plots for each parameter in a section.
    """
    for param_name, values in data.items():
        if not values:
            continue
            
        frames = list(range(1, len(values) + 1))
        
        plt.figure(figsize=(12, 6))
        plt.plot(frames, values, marker='o', linewidth=2, markersize=4)
        plt.title(f'{section_name}: {param_name} Over Time', fontsize=14, fontweight='bold')
        plt.xlabel('Frame Number', fontsize=12)
        plt.ylabel('Value', fontsize=12)
        plt.grid(True, alpha=0.3)
        
        # Add some statistics to the plot
        mean_val = np.mean(values)
        std_val = np.std(values)
        plt.axhline(y=mean_val, color='red', linestyle='--', alpha=0.7, 
                   label=f'Mean: {mean_val:.1f}')
        
        # Format the parameter name for filename
        safe_param_name = re.sub(r'[^\w\-_]', '_', param_name)
        filename = f"{prefix}_{safe_param_name}.png"
        filepath = os.path.join(output_dir, filename)
        
        plt.legend()
        plt.tight_layout()
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"  Saved: {filename}")

def create_overview_plots(config_data, dm_data, output_dir):
    """
    Create overview plots with multiple parameters in subplots.
    """
    # Config values overview
    if config_data:
        create_multi_parameter_plot(config_data, "Config Values Overview", 
                                  os.path.join(output_dir, "config_overview.png"))
    
    # DM values overview  
    if dm_data:
        create_multi_parameter_plot(dm_data, "DM Values Overview",
                                  os.path.join(output_dir, "dm_overview.png"))

def create_multi_parameter_plot(data, title, filepath):
    """
    Create a subplot figure with multiple parameters.
    """
    # Filter out parameters with no variation for cleaner plots
    varying_params = {name: values for name, values in data.items() 
                     if len(set(values)) > 1}
    
    if not varying_params:
        print(f"  No varying parameters found for {title}")
        return
    
    # Calculate subplot layout
    n_params = len(varying_params)
    n_cols = min(3, n_params)
    n_rows = (n_params + n_cols - 1) // n_cols
    
    fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 5 * n_rows))
    fig.suptitle(title, fontsize=16, fontweight='bold')
    
    # Handle single subplot case
    if n_params == 1:
        axes = [axes]
    elif n_rows == 1:
        axes = axes if n_params > 1 else [axes]
    else:
        axes = axes.flatten()
    
    for i, (param_name, values) in enumerate(varying_params.items()):
        frames = list(range(1, len(values) + 1))
        
        ax = axes[i]
        ax.plot(frames, values, marker='o', linewidth=2, markersize=3)
        ax.set_title(param_name, fontsize=11, fontweight='bold')
        ax.set_xlabel('Frame')
        ax.set_ylabel('Value')
        ax.grid(True, alpha=0.3)
        
        # Add trend line if there's a clear trend
        if len(values) > 2:
            z = np.polyfit(frames, values, 1)
            p = np.poly1d(z)
            ax.plot(frames, p(frames), "r--", alpha=0.7, linewidth=1)
    
    # Hide unused subplots
    for i in range(n_params, len(axes)):
        axes[i].set_visible(False)
    
    plt.tight_layout()
    plt.savefig(filepath, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"  Saved: {os.path.basename(filepath)}")

def print_summary(config_data, dm_data):
    """
    Print a summary of the parsed data.
    """
    print("\n" + "="*60)
    print("PARSING SUMMARY")
    print("="*60)
    
    if config_data:
        print(f"\nConfig Values ({len(config_data)} parameters):")
        for param, values in config_data.items():
            if values:
                print(f"  {param}: {len(values)} frames, "
                      f"range [{min(values)}-{max(values)}], "
                      f"varies: {len(set(values)) > 1}")
    
    if dm_data:
        print(f"\nDM Values ({len(dm_data)} parameters):")
        for param, values in dm_data.items():
            if values:
                print(f"  {param}: {len(values)} frames, "
                      f"range [{min(values)}-{max(values)}], "
                      f"varies: {len(set(values)) > 1}")
    
    total_frames = len(next(iter(config_data.values()))) if config_data else 0
    print(f"\nTotal frames processed: {total_frames}")

def main():
    """
    Main function to run the log parser and plotter.
    """
    log_filename = "TC_Flash_first_few_frames_pq_log.txt"

    print("TC Flash PQ Log Parser and Plotter")
    print("="*40)

    # Check if log file exists
    if not os.path.exists(log_filename):
        print(f"Error: Log file '{log_filename}' not found!")
        print("Please ensure the log file is in the current directory.")
        return

    print(f"Parsing log file: {log_filename}")

    # Parse the log file
    config_data, dm_data = parse_log_file(log_filename)

    # Print summary
    print_summary(config_data, dm_data)

    # Create plots
    print(f"\nCreating plots...")
    create_plots(config_data, dm_data)

    print(f"\nPlotting complete! Check the 'plots' directory for output files.")
    print(f"\nGenerated {len(config_data) + len(dm_data) + 2} individual plots plus 2 overview plots.")
    print(f"Total files created: {len(config_data) + len(dm_data) + 2} plots")

    # Summary of key findings
    print(f"\n" + "="*50)
    print("KEY FINDINGS SUMMARY")
    print("="*50)
    print("Parameters with decreasing trends:")
    print("  - All Config gain_* parameters: 127 → 42")
    print("  - DM LocalMappingStrength: 16319 → 5397")
    print("  - DM dBrightnessPRon: 16319 → 5397")
    print("  - DM dContrast/dSaturation: 8224 → 2719")
    print("  - DM dLocalContrast: 16319 → 5397")
    print("\nParameters with increasing trends:")
    print("  - DM UpMappingStrength: 32960 → 48676")
    print("\nConstant parameters:")
    print("  - Config: intensity_level, PrecisionRenderingStrength, DBrightness, etc.")
    print("  - DM: LmOn, dBrightness, TMax")
    print(f"\nTotal frames analyzed: 300")

if __name__ == "__main__":
    main()
