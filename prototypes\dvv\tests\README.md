# PQ Tuning Config Editor - Test Suite

This directory contains comprehensive tests for the PQ tuning config editor web application, organized by test type and functionality.

## Test Structure

```
tests/
├── __init__.py                      # Package initialization
├── conftest.py                      # Shared test fixtures and utilities
├── README.md                        # This documentation
├── unit/                           # Unit tests for individual components
│   ├── core/                       # Core functionality tests
│   │   ├── test_config.py
│   │   ├── test_exceptions.py
│   │   ├── test_file_type_detector.py
│   │   └── test_utils.py
│   ├── processors/                 # Log processor tests
│   │   └── test_log_processors.py
│   ├── web/                       # Web application tests
│   │   ├── test_api.py
│   │   ├── test_registry.py
│   │   └── test_session_manager.py
│   └── plugins/                   # Plugin system tests (future)
├── integration/                    # Integration tests
│   ├── test_download.py
│   ├── test_subprocess_architecture.py
│   ├── test_web_app_selection.py
│   └── test_workflow.py
├── ui/                            # UI and workflow tests
│   ├── test_firmware_log_workflow.py
│   ├── test_grid_comparison_ui.py
│   └── test_log_info_visibility.py
├── data/                          # Test data files
│   ├── configs/                   # Sample configuration files
│   ├── logs/                     # Sample log files
│   ├── json/                     # JSON test data
│   ├── reports/                  # Sample reports
│   └── ui/                       # UI test files
├── fixtures/                      # Test fixtures and utilities
├── artifacts/                     # Test output artifacts
│   ├── plots/                    # Generated test plots
│   └── reports/                  # Test reports and coverage
└── scripts/                       # Test utility scripts
    ├── cleanup_test_data.py
    ├── run_tests.py
    └── test_unified_download.py
```

## Test Files Description

### `conftest.py`
Contains shared test fixtures, utilities, and constants used across all test files:
- `TestFixtures`: Factory methods for creating test data
- `TestValidators`: Validation utilities for data structures
- `TestConstants`: Test constants and sample data

### `test_registry.py`
Comprehensive tests for the `WebApplicationRegistry` class:
- Registry initialization and configuration loading
- Application retrieval (enabled, all, specific)
- Application validation and URL generation
- Dynamic application registration
- Enable/disable functionality
- Real configuration validation

### `test_api.py`
Tests for API endpoints and session management:
- Session creation, retrieval, and validation
- User registration logic validation
- Web applications API data structure
- URL generation for different application types
- Session tracking of selected applications

### `test_web_app_selection.py`
Original tests for web application selection functionality:
- Web application registry basic functionality
- Configuration validation
- JSON serialization testing

### `test_workflow.py`
Complete workflow integration tests:
- End-to-end workflow component testing
- Persistence simulation
- English-only UI validation
- Feature summary validation

### `test_subprocess_architecture.py`
Comprehensive tests for subprocess-based log processing architecture:
- TC Flash log detection and validation
- Subprocess execution for grid_comparison_plotter.py and log_parser_plotter.py
- PNG file generation and static file management
- User selection workflow (no automatic detection)
- File type detection and processing mode routing
- Integration with web application registry

### `test_download.py`
Tests for download functionality and report generation:
- HTML report generation with proper formatting
- Markdown report generation with structured content
- JSON report generation with valid data structures
- CSV report generation for data analysis
- Content validation across all report formats
- Author attribution and metadata inclusion

## Running Tests

### Prerequisites
- Python 3.7+
- All project dependencies installed
- Working directory: `prototypes/detect_undef_pq_para/`

### Run All Tests
```bash
python run_tests.py
```

### Run Specific Test Suite
```bash
python run_tests.py --test registry              # Registry tests only
python run_tests.py --test api                   # API tests only
python run_tests.py --test workflow              # Workflow tests only
python run_tests.py --test subprocess_architecture # Subprocess tests only
python run_tests.py --test download              # Download tests only
```

### Run with Verbose Output
```bash
python run_tests.py --verbose
```

### List Available Tests
```bash
python run_tests.py --list
```

### Run Individual Test Files
```bash
cd tests/
python test_registry.py
python test_api.py
python test_web_app_selection.py
python test_workflow.py
python test_subprocess_architecture.py
python test_download.py
```

## Test Coverage

The test suite covers the following areas:

### ✅ Web Application Registry
- [x] Registry initialization from configuration
- [x] Application retrieval and filtering
- [x] Application validation
- [x] URL generation for local and external apps
- [x] Dynamic application registration
- [x] Enable/disable functionality
- [x] Error handling for invalid operations

### ✅ Session Management
- [x] Session creation with application selection
- [x] Session retrieval and validation
- [x] Session data structure validation
- [x] Session tracking of selected applications
- [x] Error handling for invalid sessions

### ✅ User Registration
- [x] Valid user ID validation
- [x] Application selection validation
- [x] Registration data structure validation
- [x] Error handling for invalid registrations

### ✅ API Data Structures
- [x] Web applications API response format
- [x] Session data format validation
- [x] Required field validation
- [x] Data type validation

### ✅ Integration Testing
- [x] Complete workflow component testing
- [x] Configuration consistency validation
- [x] UI text validation (English-only)
- [x] Feature completeness validation

### ✅ Subprocess Architecture
- [x] TC Flash log detection and file validation
- [x] Subprocess execution of external tools (grid_comparison_plotter.py, log_parser_plotter.py)
- [x] PNG file generation and static file management
- [x] User selection workflow without automatic detection
- [x] File type detection and processing mode routing
- [x] Integration with web application registry
- [x] Error handling and timeout management

### ✅ Download Functionality
- [x] HTML report generation with proper formatting
- [x] Markdown report generation with structured content
- [x] JSON report generation with valid data structures
- [x] CSV report generation for data analysis
- [x] Content validation across all report formats
- [x] Author attribution and metadata inclusion

## Expected Test Results

When all tests pass, you should see output similar to:

```
============================================================
PQ Tuning Config Editor - Test Suite
Running 6 test files...
============================================================

✓ test_registry.py PASSED (2.34s)
✓ test_api.py PASSED (1.87s)
✓ test_web_app_selection.py PASSED (0.92s)
✓ test_workflow.py PASSED (1.23s)
✓ test_subprocess_architecture.py PASSED (3.45s)
✓ test_download.py PASSED (1.15s)

============================================================
TEST SUMMARY
============================================================
PASS | test_registry.py              |   2.34s
PASS | test_api.py                   |   1.87s
PASS | test_web_app_selection.py     |   0.92s
PASS | test_workflow.py              |   1.23s
PASS | test_subprocess_architecture.py|   3.45s
PASS | test_download.py              |   1.15s
============================================================
Total: 6 tests | Passed: 6 | Failed: 0
Total Duration: 10.96s
🎉 All tests passed!
```

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure you're running tests from the correct directory
2. **Module Not Found**: Check that all project files are in place
3. **Configuration Errors**: Verify `config.py` contains valid `WEB_APPLICATIONS`
4. **Session File Errors**: Ensure write permissions in the project directory

### Debug Mode
For detailed debugging, run individual test files directly:
```bash
cd tests/
python -v test_registry.py
```

## Adding New Tests

To add new tests:

1. Create new test functions in existing files or create new test files
2. Follow the naming convention: `test_*.py` for files, `test_*()` for functions
3. Use the shared fixtures from `conftest.py`
4. Update this README if adding new test categories
5. Ensure new tests are included in the test runner

## Test Data

Tests use a combination of:
- Real configuration data from `config.py`
- Mock test data from `conftest.py`
- Temporary test sessions and registries
- Sample user IDs and application selections

All test data is cleaned up automatically after test execution.
