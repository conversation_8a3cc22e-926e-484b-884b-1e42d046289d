#!/usr/bin/env python3
"""
Unit tests for log_processors.py module

Tests log processing functionality including:
- TCFlashLogDetector class
- SubprocessLogProcessor class
- LogProcessorManager class
- LogProcessingResult data structure
"""

import pytest
import sys
from pathlib import Path
import tempfile
from unittest.mock import patch, MagicMock

# Add parent directories to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent))

from log_processors import TCFlashLogDetector, FirmwareLogDetector, SubprocessLogProcessor, LogProcessorManager, LogProcessingResult


class TestTCFlashLogDetector:
    """Test TC Flash log detection functionality"""
    
    def test_is_tc_flash_log_with_valid_content(self):
        """Test TC Flash log detection with valid content"""
        valid_content = """
        Frame 0001:
        Config Parameters:
        parameter1=value1
        parameter2=value2
        DM Parameters:
        dm_param1=value1
        """
        
        result = TCFlashLogDetector.is_tc_flash_log(valid_content)
        assert isinstance(result, bool)
    
    def test_is_tc_flash_log_with_invalid_content(self):
        """Test TC Flash log detection with invalid content"""
        invalid_content = "This is not a TC Flash log file"
        
        result = TCFlashLogDetector.is_tc_flash_log(invalid_content)
        assert result == False
    
    def test_get_log_info(self):
        """Test log information extraction"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("Frame 0001:\nConfig Parameters:\nparam1=value1\n")
            f.flush()

            file_path = Path(f.name)
            try:
                info = TCFlashLogDetector.get_log_info(file_path)

                # Check required fields
                assert 'total_frames' in info
                assert 'config_parameters' in info
                assert 'dm_parameters' in info
                assert 'total_parameters' in info

                assert isinstance(info['total_frames'], int)
                assert isinstance(info['config_parameters'], int)
                assert isinstance(info['dm_parameters'], int)
                assert isinstance(info['total_parameters'], int)

            finally:
                try:
                    file_path.unlink(missing_ok=True)
                except (PermissionError, OSError):
                    # On Windows, files might still be in use
                    pass


class TestFirmwareLogDetector:
    """Test Firmware log detection functionality"""

    def test_is_firmware_log_with_valid_dolby_content(self):
        """Test firmware log detection with valid Dolby debug content"""
        valid_content = """
        Debug output from Dolby Vision processing

        dBrightness(100)
        dContrast(85)
        dSaturation(90)
        dBacklight(75)

        gainPos_precisionRendering(50)
        gainPos_dLocalContrast(60)
        gainPos_dBrightness(45)

        confidence(95)
        precisionRenderingStrength(80)
        """

        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(valid_content)
            f.flush()

            file_path = Path(f.name)
            try:
                result = FirmwareLogDetector.is_firmware_log(file_path)
                assert result == True
            finally:
                try:
                    file_path.unlink(missing_ok=True)
                except (PermissionError, OSError):
                    pass

    def test_is_firmware_log_with_valid_level_content(self):
        """Test firmware log detection with valid level parameter content"""
        valid_content = """
        Firmware log with level parameters

        mid_boost: 40
        highlight_stretch: 35
        shadow_drop: 20
        contrast_boost: 25
        saturation_boost: 30
        detail_boost: 15
        chroma_indicator: 85
        intensity_indicator_PQ: 90
        """

        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(valid_content)
            f.flush()

            file_path = Path(f.name)
            try:
                result = FirmwareLogDetector.is_firmware_log(file_path)
                assert result == True
            finally:
                try:
                    file_path.unlink(missing_ok=True)
                except (PermissionError, OSError):
                    pass

    def test_is_firmware_log_with_structured_content(self):
        """Test firmware log detection with structured format content"""
        valid_content = """
        Structured firmware log

        intensity_level: 100
        gain_PrecisionRenderingStrength: 127
        gain_DLocalContrast: 80
        LocalMappingStrength 85
        TMax 4000
        UpMappingStrength=90
        """

        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(valid_content)
            f.flush()

            file_path = Path(f.name)
            try:
                result = FirmwareLogDetector.is_firmware_log(file_path)
                assert result == True
            finally:
                try:
                    file_path.unlink(missing_ok=True)
                except (PermissionError, OSError):
                    pass

    def test_is_firmware_log_with_general_parameters(self):
        """Test firmware log detection with general parameter patterns"""
        valid_content = """
        General parameter log

        param1: 100
        param2=200
        param3 300
        param4(400)
        param5: 500
        param6=600
        param7 700
        param8(800)
        param9: 900
        param10=1000
        param11 1100
        """

        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(valid_content)
            f.flush()

            file_path = Path(f.name)
            try:
                result = FirmwareLogDetector.is_firmware_log(file_path)
                assert result == True
            finally:
                try:
                    file_path.unlink(missing_ok=True)
                except (PermissionError, OSError):
                    pass

    def test_is_firmware_log_with_invalid_content(self):
        """Test firmware log detection with invalid content"""
        invalid_content = "This is not a firmware log file with no parameters"

        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(invalid_content)
            f.flush()

            file_path = Path(f.name)
            try:
                result = FirmwareLogDetector.is_firmware_log(file_path)
                assert result == False
            finally:
                try:
                    file_path.unlink(missing_ok=True)
                except (PermissionError, OSError):
                    pass

    def test_is_firmware_log_nonexistent_file(self):
        """Test firmware log detection with nonexistent file"""
        nonexistent_file = Path("nonexistent_file.txt")
        result = FirmwareLogDetector.is_firmware_log(nonexistent_file)
        assert result == False

    def test_get_log_info(self):
        """Test firmware log information extraction"""
        valid_content = """
        dBrightness(100)
        dContrast(85)
        gainPos_precisionRendering(50)
        mid_boost: 40
        intensity_level: 100
        LocalMappingStrength 85
        """

        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(valid_content)
            f.flush()

            file_path = Path(f.name)
            try:
                info = FirmwareLogDetector.get_log_info(file_path)

                # Check required fields
                assert 'dolby_parameters' in info
                assert 'level_parameters' in info
                assert 'structured_parameters' in info
                assert 'total_unique_parameters' in info
                assert 'total_measurements' in info
                assert 'file_size_bytes' in info
                assert 'estimated_processable' in info

                assert isinstance(info['dolby_parameters'], int)
                assert isinstance(info['level_parameters'], int)
                assert isinstance(info['structured_parameters'], int)
                assert isinstance(info['total_unique_parameters'], int)
                assert isinstance(info['total_measurements'], int)
                assert isinstance(info['file_size_bytes'], int)
                assert isinstance(info['estimated_processable'], bool)

                # Should detect some parameters
                assert info['total_measurements'] > 0
                assert info['estimated_processable'] == True

            finally:
                try:
                    file_path.unlink(missing_ok=True)
                except (PermissionError, OSError):
                    pass


class TestLogProcessingResult:
    """Test LogProcessingResult data structure"""
    
    def test_log_processing_result_creation(self):
        """Test creating LogProcessingResult instances"""
        result = LogProcessingResult(
            success=True,
            message="Test message",
            processor_used="test_processor"
        )
        
        assert result.success == True
        assert result.message == "Test message"
        assert result.processor_used == "test_processor"


class TestSubprocessLogProcessor:
    """Test subprocess log processing functionality"""
    
    @patch('subprocess.run')
    def test_execute_grid_comparison_plotter(self, mock_subprocess):
        """Test grid comparison plotter execution"""
        # Mock successful subprocess execution
        mock_subprocess.return_value = MagicMock(
            returncode=0,
            stdout="Success",
            stderr=""
        )
        
        with tempfile.TemporaryDirectory() as temp_dir:
            input_file = Path(temp_dir) / "test.txt"
            output_dir = Path(temp_dir) / "output"
            output_dir.mkdir()
            
            input_file.write_text("test content")
            
            result = SubprocessLogProcessor.execute_grid_comparison_plotter(input_file, output_dir)
            
            assert isinstance(result, LogProcessingResult)
            assert hasattr(result, 'success')
            assert hasattr(result, 'message')
            assert hasattr(result, 'processor_used')


class TestLogProcessorManager:
    """Test log processor management"""
    
    def test_process_log_file_with_grid_comparison(self):
        """Test processing log file with grid comparison processor"""
        with tempfile.TemporaryDirectory() as temp_dir:
            input_file = Path(temp_dir) / "test.txt"
            output_dir = Path(temp_dir) / "output"
            output_dir.mkdir()

            # Write valid TC Flash log content
            tc_flash_content = """Frame 0001:
Config values:
param1=value1
param2=value2

DM values:
dm_param1=dm_value1
dm_param2=dm_value2

fallback: 0
"""
            input_file.write_text(tc_flash_content)

            with patch.object(SubprocessLogProcessor, 'execute_grid_comparison_plotter') as mock_execute:
                mock_execute.return_value = LogProcessingResult(
                    success=True,
                    message="Test success",
                    processor_used="grid_comparison"
                )

                result = LogProcessorManager.process_log_file(input_file, output_dir, "grid_comparison")

                assert isinstance(result, LogProcessingResult)
                assert result.success == True
                assert result.processor_used == "grid_comparison"
    
    def test_process_log_file_with_unknown_processor(self):
        """Test processing with unknown processor type"""
        with tempfile.TemporaryDirectory() as temp_dir:
            input_file = Path(temp_dir) / "test.txt"
            output_dir = Path(temp_dir) / "output"
            output_dir.mkdir()
            
            input_file.write_text("test content")
            
            result = LogProcessorManager.process_log_file(input_file, output_dir, "unknown_processor")
            
            assert isinstance(result, LogProcessingResult)
            assert result.success == False
            assert "Unknown processor type" in result.message


if __name__ == "__main__":
    pytest.main([__file__])
