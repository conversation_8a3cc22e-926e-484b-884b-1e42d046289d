#!/usr/bin/env python3
"""
Comprehensive tests for Web Application Registry
Author: <PERSON> <<EMAIL>>
"""

import sys
from pathlib import Path

# Add parent directory to path to import local modules
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent))

from web_app_registry import WebApplicationRegistry, WebApplication
from config import WEB_APPLICATIONS
from tests.conftest import TestFixtures, TestValidators, TestConstants

def test_registry_initialization():
    """Test web application registry initialization"""
    print("Testing Registry Initialization...")
    
    registry = WebApplicationRegistry()
    
    # Test that registry loads applications from config
    assert len(registry.applications) > 0, "Registry should load applications from config"
    
    # Test that all applications are WebApplication instances
    for app_id, app in registry.applications.items():
        TestValidators.validate_web_application(app)
        assert app.id == app_id, f"Application ID mismatch: {app.id} != {app_id}"
    
    print(f"   [OK] Registry initialized with {len(registry.applications)} applications")

def test_get_enabled_applications():
    """Test getting enabled applications"""
    print("Testing Get Enabled Applications...")
    
    registry = TestFixtures.create_test_registry()
    enabled_apps = registry.get_enabled_applications()
    
    # Should return only enabled applications (test_app_1, test_app_2, grid_comparison_plotter, log_parser_plotter)
    assert len(enabled_apps) == 4, f"Expected 4 enabled apps, got {len(enabled_apps)}"
    
    for app in enabled_apps:
        assert app.enabled == True, "All returned apps should be enabled"
        TestValidators.validate_web_application(app)
    
    print(f"   [OK] Found {len(enabled_apps)} enabled applications")

def test_get_all_applications():
    """Test getting all applications"""
    print("Testing Get All Applications...")
    
    registry = TestFixtures.create_test_registry()
    all_apps = registry.get_all_applications()
    
    # Should return all applications (enabled and disabled)
    assert len(all_apps) == 5, f"Expected 5 total apps, got {len(all_apps)}"

    enabled_count = sum(1 for app in all_apps if app.enabled)
    disabled_count = sum(1 for app in all_apps if not app.enabled)

    assert enabled_count == 4, f"Expected 4 enabled apps, got {enabled_count}"
    assert disabled_count == 1, f"Expected 1 disabled app, got {disabled_count}"
    
    print(f"   [OK] Found {len(all_apps)} total applications ({enabled_count} enabled, {disabled_count} disabled)")

def test_get_application():
    """Test getting specific application"""
    print("Testing Get Specific Application...")
    
    registry = TestFixtures.create_test_registry()
    
    # Test getting existing application
    app = registry.get_application("test_app_1")
    assert app is not None, "Should find existing application"
    assert app.id == "test_app_1", "Should return correct application"
    TestValidators.validate_web_application(app)
    
    # Test getting non-existent application
    app = registry.get_application("nonexistent_app")
    assert app is None, "Should return None for non-existent application"
    
    print("   [OK] Application retrieval works correctly")

def test_is_valid_application():
    """Test application validation"""
    print("Testing Application Validation...")
    
    registry = TestFixtures.create_test_registry()
    
    # Test valid enabled applications
    for app_id in TestConstants.VALID_APP_IDS[:2]:  # test_app_1, test_app_2
        assert registry.is_valid_application(app_id), f"{app_id} should be valid"
    
    # Test disabled application
    assert not registry.is_valid_application("disabled_app"), "Disabled app should not be valid"
    
    # Test non-existent application
    assert not registry.is_valid_application("nonexistent_app"), "Non-existent app should not be valid"
    
    # Test empty/None application ID
    assert not registry.is_valid_application(""), "Empty app ID should not be valid"
    assert not registry.is_valid_application(None), "None app ID should not be valid"
    
    print("   [OK] Application validation works correctly")

def test_get_application_url():
    """Test application URL generation"""
    print("Testing Application URL Generation...")
    
    registry = TestFixtures.create_test_registry()
    
    # Test local application URL
    url = registry.get_application_url("test_app_1", "test_user")
    assert url == "/test1", f"Expected '/test1', got '{url}'"
    
    # Test external application URL
    url = registry.get_application_url("test_app_2", "test_user")
    assert url == "http://localhost:8000", f"Expected 'http://localhost:8000', got '{url}'"

    # Test new bin applications
    url = registry.get_application_url("grid_comparison_plotter", "test_user")
    assert url == "http://localhost:8000", f"Expected 'http://localhost:8000', got '{url}'"

    url = registry.get_application_url("log_parser_plotter", "test_user")
    assert url == "http://localhost:8000", f"Expected 'http://localhost:8000', got '{url}'"

    # Test disabled application
    url = registry.get_application_url("disabled_app", "test_user")
    assert url is None, "Disabled app should return None URL"

    # Test non-existent application
    url = registry.get_application_url("nonexistent_app", "test_user")
    assert url is None, "Non-existent app should return None URL"

    print("   [OK] URL generation works correctly")

def test_register_application():
    """Test dynamic application registration"""
    print("Testing Dynamic Application Registration...")
    
    registry = TestFixtures.create_test_registry()
    initial_count = len(registry.applications)
    
    # Test registering new application
    new_app_config = {
        "name": "Dynamic Test App",
        "description": "Dynamically registered test application",
        "icon": "fas fa-test",
        "url": "/dynamic",
        "enabled": True,
        "author": "Test Author",
        "version": "1.0.0"
    }
    
    success = registry.register_application("dynamic_app", new_app_config)
    assert success, "Application registration should succeed"
    assert len(registry.applications) == initial_count + 1, "Registry should have one more application"
    
    # Verify the registered application
    app = registry.get_application("dynamic_app")
    assert app is not None, "Registered application should be retrievable"
    assert app.name == "Dynamic Test App", "Application name should match"
    TestValidators.validate_web_application(app)
    
    # Test registering application with missing required fields
    invalid_config = {"name": "Incomplete App"}
    success = registry.register_application("invalid_app", invalid_config)
    assert not success, "Registration with incomplete config should fail"
    
    print("   [OK] Dynamic application registration works correctly")

def test_enable_disable_application():
    """Test enabling and disabling applications"""
    print("Testing Enable/Disable Application...")
    
    registry = TestFixtures.create_test_registry()
    
    # Test enabling disabled application
    success = registry.enable_application("disabled_app")
    assert success, "Enabling existing app should succeed"
    
    app = registry.get_application("disabled_app")
    assert app.enabled == True, "Application should be enabled"
    assert registry.is_valid_application("disabled_app"), "Enabled app should be valid"
    
    # Test disabling enabled application
    success = registry.disable_application("test_app_1")
    assert success, "Disabling existing app should succeed"
    
    app = registry.get_application("test_app_1")
    assert app.enabled == False, "Application should be disabled"
    assert not registry.is_valid_application("test_app_1"), "Disabled app should not be valid"
    
    # Test enabling/disabling non-existent application
    success = registry.enable_application("nonexistent_app")
    assert not success, "Enabling non-existent app should fail"
    
    success = registry.disable_application("nonexistent_app")
    assert not success, "Disabling non-existent app should fail"
    
    print("   [OK] Enable/disable functionality works correctly")

def test_real_config_applications():
    """Test with real configuration applications"""
    print("Testing Real Configuration Applications...")
    
    registry = WebApplicationRegistry()
    
    # Test that PQ Config Validator exists and is enabled
    pq_app = registry.get_application("pq_config_validator")
    assert pq_app is not None, "PQ Config Validator should exist"
    assert pq_app.enabled == True, "PQ Config Validator should be enabled"
    assert pq_app.name == "PQ Config Validator", "PQ Config Validator name should match"
    
    # Test that all configured applications are valid
    for app_id in WEB_APPLICATIONS.keys():
        app = registry.get_application(app_id)
        assert app is not None, f"Configured application {app_id} should exist"
        TestValidators.validate_web_application(app)
    
    print(f"   [OK] All {len(WEB_APPLICATIONS)} configured applications are valid")

def test_new_bin_applications():
    """Test the newly added bin applications"""
    print("Testing New Bin Applications...")

    registry = TestFixtures.create_test_registry()

    # Test grid_comparison_plotter
    grid_app = registry.get_application("grid_comparison_plotter")
    assert grid_app is not None, "grid_comparison_plotter should exist"
    assert grid_app.name == "Grid Comparison Plotter", "Incorrect name for grid_comparison_plotter"
    assert grid_app.enabled == True, "grid_comparison_plotter should be enabled"
    assert grid_app.url == "http://localhost:8000", "Incorrect URL for grid_comparison_plotter"
    assert grid_app.icon == "fas fa-th", "Incorrect icon for grid_comparison_plotter"
    assert "TC Flash PQ log analysis" in grid_app.description, "Incorrect description for grid_comparison_plotter"

    # Test log_parser_plotter
    log_app = registry.get_application("log_parser_plotter")
    assert log_app is not None, "log_parser_plotter should exist"
    assert log_app.name == "Log Parser Plotter", "Incorrect name for log_parser_plotter"
    assert log_app.enabled == True, "log_parser_plotter should be enabled"
    assert log_app.url == "http://localhost:8000", "Incorrect URL for log_parser_plotter"
    assert log_app.icon == "fas fa-chart-line", "Incorrect icon for log_parser_plotter"
    assert "TC Flash PQ log parser" in log_app.description, "Incorrect description for log_parser_plotter"

    # Test validation
    assert registry.is_valid_application("grid_comparison_plotter"), "grid_comparison_plotter should be valid"
    assert registry.is_valid_application("log_parser_plotter"), "log_parser_plotter should be valid"

    print("   [OK] Grid Comparison Plotter configuration verified")
    print("   [OK] Log Parser Plotter configuration verified")
    print("New Bin Applications test passed!\n")

if __name__ == "__main__":
    print("=" * 60)
    print("Web Application Registry Tests")
    print("=" * 60)
    
    try:
        test_registry_initialization()
        test_get_enabled_applications()
        test_get_all_applications()
        test_get_application()
        test_is_valid_application()
        test_get_application_url()
        test_register_application()
        test_enable_disable_application()
        test_real_config_applications()
        test_new_bin_applications()

        print("=" * 60)
        print("All registry tests passed! [OK]")
        print("=" * 60)
        
    except Exception as e:
        print(f"Registry test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
