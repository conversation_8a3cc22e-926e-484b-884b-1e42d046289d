[tool:pytest]
# Pytest configuration for PQ Tuning Config Editor
# This configuration ensures proper test discovery and execution
# with the reorganized test directory structure

# Test discovery patterns
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Test collection patterns for the new directory structure
# Unit tests: tests/unit/**/*
# Integration tests: tests/integration/*
# UI tests: tests/ui/*
# Test scripts: tests/scripts/* (excluded from automatic discovery)

# Minimum version requirement
minversion = 6.0

# Add current directory to Python path for imports
addopts = 
    --tb=short
    --strict-markers
    --disable-warnings
    --verbose
    -p no:cacheprovider

# Markers for test categorization
markers =
    unit: Unit tests for individual modules and functions
    integration: Integration tests for component interactions
    ui: User interface and workflow tests
    slow: Tests that take longer to execute
    network: Tests that require network connectivity
    subprocess: Tests that use subprocess execution
    
# Test timeout (in seconds)
timeout = 300

# Ignore patterns
norecursedirs = 
    .git
    .tox
    dist
    build
    *.egg
    __pycache__
    .pytest_cache
    uploads
    logs
    static
    templates
    bin
    data
    pq_config_validator

# Coverage settings (if pytest-cov is installed)
# addopts = --cov=. --cov-report=html --cov-report=term-missing

# Logging configuration for tests
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Filterwarnings to suppress known warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:requests.*
