# PQ Config Validator

A professional FastAPI web application for validating PQ (Picture Quality) configuration files.

**Author:** <PERSON> <<EMAIL>>
**Version:** 1.0.0
**Copyright:** (c) 2025 Dolby Laboratories, Inc.

## Features

- **Dual Analysis Engine**: Combines pattern-based parameter detection with external DolbyVision tool validation
- **Professional Validation**: Industry-standard compliance checking using DolbyVision tools
- **User Session Management**: Unique user IDs with persistent sessions
- **File Upload**: Secure file upload with validation and size limits
- **Advanced Parameter Detection**: Multiple pattern matching for undefined parameters
- **Comprehensive Reporting**: Detailed analysis with downloadable results in JSON, CSV, and TXT formats
- **Web Interface**: Clean, responsive UI built with Bootstrap
- **Error Handling**: Comprehensive error handling and logging
- **LAN Deployment**: Designed for local area network access

## Supported File Types

- `.txt` - Text files
- `.cfg` - Configuration files
- `.json` - JSON files
- `.log` - Log files
- `.config` - Configuration files
- `.ini` - INI files
- `.conf` - Configuration files
- `.properties` - Properties files

## Parameter Patterns Detected

The tool can detect various parameter reference patterns:

1. `{parameter_name}` - Curly braces
2. `${parameter_name}` - Shell-style variables
3. `$parameter_name` - Simple shell variables
4. `%parameter_name%` - Windows-style variables
5. `@parameter_name@` - Autotools-style variables

## Installation

1. **Clone or download the project files**

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application:**
   ```bash
   python run.py
   ```

   Or with custom settings:
   ```bash
   python run.py --host 0.0.0.0 --port 8080 --log-level DEBUG
   ```

## Usage

1. **Access the application** in your web browser at `http://localhost:8000`

2. **Enter a unique user ID** on the home page

3. **Upload a text file** for analysis

4. **View the results** showing any undefined parameters with their line numbers

## Configuration

The application can be configured through environment variables or by modifying `config.py`:

- `HOST`: Server host (default: 0.0.0.0)
- `PORT`: Server port (default: 8000)
- `DEBUG`: Debug mode (default: false)
- `LOG_LEVEL`: Logging level (default: INFO)
- `SESSION_TIMEOUT_HOURS`: Session timeout in hours (default: 24)

## File Structure

```
prototypes/detect_undef_pq_para/
├── main.py              # Main FastAPI application
├── run.py               # Startup script
├── config.py            # Configuration settings
├── utils.py             # Utility functions
├── exceptions.py        # Custom exceptions
├── requirements.txt     # Python dependencies
├── templates/           # HTML templates
│   ├── base.html
│   ├── index.html
│   └── upload.html
├── static/              # Static assets
│   ├── css/
│   └── js/
├── uploads/             # User uploaded files
├── data/                # Session data
└── logs/                # Application logs
```

## API Endpoints

- `GET /` - Home page
- `POST /register` - Register user ID
- `GET /upload/{session_id}` - File upload page
- `POST /api/upload/{session_id}` - Upload and process file
- `GET /api/files/{session_id}` - List user files
- `DELETE /api/files/{session_id}/{filename}` - Delete file
- `GET /api/session/{session_id}` - Get session info
- `GET /api/download-results/{session_id}` - Download analysis results (JSON/CSV/TXT)

## Security Features

- File type validation
- File size limits (10MB default)
- Filename sanitization
- Directory traversal protection
- Session timeout
- Input validation

## Logging

The application logs to both console and file (`logs/app.log`). Log levels can be configured through the `LOG_LEVEL` environment variable.

## Development

For development with auto-reload:

```bash
python run.py --reload --log-level DEBUG
```

## Deployment

For production deployment:

1. Set environment variables for production settings
2. Use a production WSGI server like Gunicorn
3. Configure reverse proxy (nginx/Apache) if needed
4. Set up proper logging and monitoring

## Author

**Ethan Li** <<EMAIL>>
Dolby Laboratories, Inc.

## License

This project is proprietary software developed for Dolby Laboratories.
Copyright (c) 2025 Dolby Laboratories, Inc. All rights reserved.
