#!/usr/bin/env python3
"""
Unit tests for session management functionality

Tests session creation, management, and validation:
- SessionManager class functionality
- Session data structures
- Session persistence and retrieval
- Session validation and cleanup
"""

import pytest
import sys
from pathlib import Path
import tempfile
import json
from unittest.mock import patch, MagicMock

# Add parent directories to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent))

from main import SessionManager


class TestSessionManager:
    """Test session management functionality"""
    
    def test_session_manager_creation(self):
        """Test SessionManager can be instantiated"""
        with tempfile.TemporaryDirectory() as temp_dir:
            session_file = Path(temp_dir) / "sessions.json"

            # Mock the SESSIONS_FILE constant to use our temp file
            with patch('main.SESSIONS_FILE', session_file):
                manager = SessionManager()
                assert manager is not None
    
    def test_create_session(self):
        """Test session creation"""
        with tempfile.TemporaryDirectory() as temp_dir:
            session_file = Path(temp_dir) / "sessions.json"

            # Mock the SESSIONS_FILE constant to use our temp file
            with patch('main.SESSIONS_FILE', session_file):
                manager = SessionManager()

                session_id = manager.create_session("test_user", "test_app")

                assert session_id is not None
                assert isinstance(session_id, str)
                assert len(session_id) > 0
    
    def test_get_session(self):
        """Test session retrieval"""
        with tempfile.TemporaryDirectory() as temp_dir:
            session_file = Path(temp_dir) / "sessions.json"

            # Mock the SESSIONS_FILE constant to use our temp file
            with patch('main.SESSIONS_FILE', session_file):
                manager = SessionManager()

                # Create a session first
                session_id = manager.create_session("test_user", "test_app")

                # Retrieve the session
                session = manager.get_session(session_id)

                assert session is not None
                assert session.get("user_id") == "test_user"
                assert session.get("selected_app") == "test_app"
    
    def test_get_nonexistent_session(self):
        """Test retrieving non-existent session"""
        with tempfile.TemporaryDirectory() as temp_dir:
            session_file = Path(temp_dir) / "sessions.json"

            # Mock the SESSIONS_FILE constant to use our temp file
            with patch('main.SESSIONS_FILE', session_file):
                manager = SessionManager()

                session = manager.get_session("nonexistent_session_id")

                assert session is None
    
    def test_session_persistence(self):
        """Test that sessions are persisted to file"""
        with tempfile.TemporaryDirectory() as temp_dir:
            session_file = Path(temp_dir) / "sessions.json"

            # Mock the SESSIONS_FILE constant to use our temp file
            with patch('main.SESSIONS_FILE', session_file):
                manager = SessionManager()

                # Create a session
                session_id = manager.create_session("test_user", "test_app")

                # Create a new manager instance to test persistence
                manager2 = SessionManager()

                # Should be able to retrieve the session
                session = manager2.get_session(session_id)

                assert session is not None
                assert session.get("user_id") == "test_user"
    
    def test_session_data_structure(self):
        """Test session data structure contains required fields"""
        with tempfile.TemporaryDirectory() as temp_dir:
            session_file = Path(temp_dir) / "sessions.json"

            # Mock the SESSIONS_FILE constant to use our temp file
            with patch('main.SESSIONS_FILE', session_file):
                manager = SessionManager()

                session_id = manager.create_session("test_user", "test_app")
                session = manager.get_session(session_id)

                # Check required fields
                required_fields = ["user_id", "selected_app", "created_at"]
                for field in required_fields:
                    assert field in session, f"Missing required field: {field}"

                # Check data types
                assert isinstance(session["user_id"], str)
                assert isinstance(session["selected_app"], str)
                # created_at is a datetime object, not string in memory
                from datetime import datetime
                assert isinstance(session["created_at"], datetime)


if __name__ == "__main__":
    pytest.main([__file__])
