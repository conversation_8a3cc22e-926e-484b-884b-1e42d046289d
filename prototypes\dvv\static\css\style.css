/* Custom styles for the Parameter Detection Tool */

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.navbar-brand {
    font-weight: 600;
}

.card {
    border: none;
    border-radius: 10px;
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
    border: none;
}

.btn {
    border-radius: 6px;
    font-weight: 500;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.form-control {
    border-radius: 6px;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-select {
    border-radius: 6px;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Web Application Selection Styling */
.web-app-selector {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.web-app-option {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.web-app-option:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

.alert {
    border-radius: 6px;
    border: none;
}

.file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: #007bff;
    background-color: #f8f9ff;
}

.file-upload-area.dragover {
    border-color: #007bff;
    background-color: #e3f2fd;
}

.file-info {
    background-color: #e9ecef;
    border-radius: 6px;
    padding: 1rem;
    margin-top: 1rem;
}

.parameter-result {
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.parameter-result:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.line-number {
    background-color: #007bff;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 600;
}

.parameter-name {
    font-family: 'Courier New', monospace;
    background-color: #f8f9fa;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 600;
}

.line-content {
    font-family: 'Courier New', monospace;
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 4px;
    border-left: 3px solid #007bff;
    margin-top: 0.5rem;
    white-space: pre-wrap;
    word-break: break-all;
}

.validation-warning {
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.validation-warning:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.validation-warning.warning-error {
    border-left: 4px solid #dc3545;
    background-color: #fff5f5;
}

.validation-warning.warning-info {
    border-left: 4px solid #ffc107;
    background-color: #fffbf0;
}

.warning-message {
    font-weight: 500;
    color: #495057;
}

.warning-details {
    font-size: 0.875rem;
    color: #6c757d;
}

.warning-details code {
    background-color: #e9ecef;
    padding: 0.125rem 0.25rem;
    border-radius: 3px;
    font-size: 0.8rem;
}

.processing-spinner {
    display: none;
}

.processing-spinner.show {
    display: block;
}

.results-container {
    display: none;
}

.results-container.show {
    display: block;
}

footer {
    margin-top: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .card {
        margin: 0 10px;
    }
}

/* Animation for loading states */
@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

.loading {
    animation: pulse 1.5s ease-in-out infinite;
}

/* Unified Download Button Component */
.unified-download-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    padding: 8px 16px;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.unified-download-btn:hover {
    background: linear-gradient(135deg, #20c997, #17a2b8);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
    color: white;
    text-decoration: none;
}

.unified-download-btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    color: white;
    text-decoration: none;
}

.unified-download-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.unified-download-btn.btn-sm {
    padding: 6px 12px;
    font-size: 0.875rem;
}

.unified-download-btn.btn-lg {
    padding: 12px 24px;
    font-size: 1.125rem;
}

.unified-download-btn .download-icon {
    font-size: 1em;
}
