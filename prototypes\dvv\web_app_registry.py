#!/usr/bin/env python3
"""
Web Application Registry for PQ Tuning Config Editor

This module provides a registry system for managing web applications
within the PQ tuning configuration editor framework.

Author: <PERSON> <<EMAIL>>
"""

import logging
from typing import Dict, List, Optional
from dataclasses import dataclass
from config import WEB_APPLICATIONS

# Setup logging
logger = logging.getLogger('web_app_registry')


@dataclass
class WebApplication:
    """Data class representing a web application"""
    id: str
    name: str
    description: str
    icon: str
    url: str
    enabled: bool
    author: str
    version: str


class WebApplicationRegistry:
    """Registry for managing web applications"""
    
    def __init__(self):
        """Initialize the registry with applications from config"""
        self.applications: Dict[str, WebApplication] = {}
        self._load_applications()
    
    def _load_applications(self):
        """Load applications from configuration"""
        for app_id, app_config in WEB_APPLICATIONS.items():
            self.applications[app_id] = WebApplication(
                id=app_id,
                name=app_config["name"],
                description=app_config["description"],
                icon=app_config["icon"],
                url=app_config["url"],
                enabled=app_config["enabled"],
                author=app_config["author"],
                version=app_config["version"]
            )
        
        logger.info(f"Loaded {len(self.applications)} web applications")
    
    def get_enabled_applications(self) -> List[WebApplication]:
        """Get list of enabled applications"""
        return [app for app in self.applications.values() if app.enabled]
    
    def get_all_applications(self) -> List[WebApplication]:
        """Get list of all applications"""
        return list(self.applications.values())
    
    def get_application(self, app_id: str) -> Optional[WebApplication]:
        """Get specific application by ID"""
        return self.applications.get(app_id)
    
    def is_valid_application(self, app_id: str) -> bool:
        """Check if application ID is valid and enabled"""
        app = self.get_application(app_id)
        return app is not None and app.enabled
    
    def get_application_url(self, app_id: str, user_id: str) -> Optional[str]:
        """Get application URL for specific user"""
        app = self.get_application(app_id)
        if not app or not app.enabled:
            return None

        # For now, return the base URL
        # In the future, this could be customized per user
        return app.url
    
    def register_application(self, app_id: str, app_config: dict) -> bool:
        """Register a new application dynamically"""
        required_fields = ["name", "description", "icon", "url", "enabled", "author", "version"]
        
        # Validate required fields
        for field in required_fields:
            if field not in app_config:
                logger.error(f"Failed to register application {app_id}: missing '{field}'")
                return False
        
        # Create and register the application
        self.applications[app_id] = WebApplication(
            id=app_id,
            name=app_config["name"],
            description=app_config["description"],
            icon=app_config["icon"],
            url=app_config["url"],
            enabled=app_config["enabled"],
            author=app_config["author"],
            version=app_config["version"]
        )
        
        logger.info(f"Registered new application: {app_id}")
        return True
    
    def enable_application(self, app_id: str) -> bool:
        """Enable an application"""
        app = self.get_application(app_id)
        if app:
            app.enabled = True
            logger.info(f"Enabled application: {app_id}")
            return True
        return False
    
    def disable_application(self, app_id: str) -> bool:
        """Disable an application"""
        app = self.get_application(app_id)
        if app:
            app.enabled = False
            logger.info(f"Disabled application: {app_id}")
            return True
        return False


# Global registry instance
web_app_registry = WebApplicationRegistry()
