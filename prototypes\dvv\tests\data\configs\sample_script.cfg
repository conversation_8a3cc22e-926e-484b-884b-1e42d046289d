# Configuration Script with Various Parameter Patterns
# This file tests different parameter reference styles

# Define some parameters
BASE_DIR=/opt/myapp
LOG_LEVEL=INFO
MAX_WORKERS=4

# Script commands with parameter references
echo "Starting application in directory: {BASE_DIR}"
echo "Log level set to: {LOG_LEVEL}"
echo "Maximum workers: {MAX_WORKERS}"

# Undefined parameters in various contexts
mkdir -p {DATA_DIR}/logs          # DATA_DIR is undefined
chmod 755 {SCRIPT_DIR}/bin/*      # SCRIPT_DIR is undefined
chown ${APP_USER}:${APP_GROUP} {CONFIG_FILE}  # APP_USER, APP_GROUP, CONFIG_FILE are undefined

# Environment variable exports
export PATH=$PATH:${CUSTOM_BIN_DIR}    # CUSTOM_BIN_DIR is undefined
export LD_LIBRARY_PATH=${LIB_PATH}     # LIB_PATH is undefined
export JAVA_HOME=${JAVA_INSTALLATION} # JAVA_INSTALLATION is undefined

# Windows batch-style commands
set TEMP_PATH=%TEMP_ROOT%\temp         # TEMP_ROOT is undefined
set LOG_DIR=%APP_HOME%\logs            # APP_HOME is undefined
copy "%SOURCE_FILE%" "%DEST_DIR%\"     # SOURCE_FILE and DEST_DIR are undefined

# Configuration file includes
include {INCLUDE_DIR}/common.conf      # INCLUDE_DIR is undefined
source ${CONFIG_HOME}/environment.sh  # CONFIG_HOME is undefined

# Database connection strings
DB_URL="postgresql://{DB_USER}:{DB_PASS}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
# DB_USER, DB_PASS, DB_HOST, DB_PORT, DB_NAME are all undefined

# Service configuration
[service]
name=myservice
user={SERVICE_USER}                    # SERVICE_USER is undefined
group={SERVICE_GROUP}                  # SERVICE_GROUP is undefined
working_directory={SERVICE_HOME}       # SERVICE_HOME is undefined
environment_file={ENV_FILE}            # ENV_FILE is undefined

[logging]
file={LOG_FILE}                        # LOG_FILE is undefined
level={LOG_LEVEL}                      # This one IS defined above
rotation_size={LOG_ROTATION_SIZE}      # LOG_ROTATION_SIZE is undefined

# Complex parameter substitutions
COMMAND="docker run -d --name {CONTAINER_NAME} -p {HOST_PORT}:{CONTAINER_PORT} -v {VOLUME_PATH}:/data {IMAGE_NAME}:{IMAGE_TAG}"
# All parameters in this command are undefined

# Autotools-style parameters
prefix=@prefix@
exec_prefix=@exec_prefix@
bindir=@bindir@
sbindir=@sbindir@
libexecdir=@libexecdir@
datarootdir=@datarootdir@
datadir=@datadir@
sysconfdir=@sysconfdir@
localstatedir=@localstatedir@

# Custom autotools parameters (likely undefined)
custom_config_dir=@custom_config_dir@
plugin_dir=@plugin_dir@
theme_dir=@theme_dir@
