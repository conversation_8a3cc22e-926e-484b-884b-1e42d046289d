# PQ Config Validator Production Environment Configuration
# Author: <PERSON>
# Copy this file to .env for production deployment

# Application Settings
HOST=0.0.0.0
PORT=8000
DEBUG=false
LOG_LEVEL=WARNING

# Session Configuration
SESSION_TIMEOUT_HOURS=24

# File Upload Settings
MAX_FILE_SIZE=10485760  # 10MB in bytes
ALLOWED_EXTENSIONS=.txt,.cfg

# Security Settings (uncomment and set in production)
# SECRET_KEY=your-secret-key-here
# CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Database Settings (if using external database)
# DATABASE_URL=postgresql://user:password@localhost:5432/pq_validator

# External Tool Configuration
# EXTERNAL_TOOL_PATH=/app/bin/dtv_config_lib_test.exe
# EXTERNAL_TOOL_TIMEOUT=30

# Logging Configuration
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
LOG_FILE=/app/logs/pq_validator.log

# Performance Settings
WORKERS=1
WORKER_CLASS=uvicorn.workers.UvicornWorker
WORKER_CONNECTIONS=1000
MAX_REQUESTS=1000
MAX_REQUESTS_JITTER=100

# Health Check Settings
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10

# Network Settings for LAN Access
# Uncomment and modify for specific network configuration
# BIND_ADDRESS=0.0.0.0
# TRUSTED_HOSTS=***********/24,10.0.0.0/8,**********/12

# Monitoring and Metrics (optional)
# ENABLE_METRICS=true
# METRICS_PORT=9090

# Backup Settings (optional)
# BACKUP_ENABLED=true
# BACKUP_INTERVAL_HOURS=24
# BACKUP_RETENTION_DAYS=7
