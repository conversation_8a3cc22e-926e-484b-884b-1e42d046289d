#!/bin/bash

# PQ Config Validator Docker Deployment Script
# Author: <PERSON> Li
# Version: 1.0.0

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="PQ Config Validator"
IMAGE_NAME="pq-config-validator"
CONTAINER_NAME="pq-config-validator"
DEFAULT_PORT="8000"

# Functions
print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  $APP_NAME Docker Deployment${NC}"
    echo -e "${BLUE}  Author: Ethan Li${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if Docker is installed and running
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    print_success "Docker is installed and running"
}

# Check if Docker Compose is available
check_docker_compose() {
    if command -v docker-compose &> /dev/null; then
        COMPOSE_CMD="docker-compose"
    elif docker compose version &> /dev/null; then
        COMPOSE_CMD="docker compose"
    else
        print_error "Docker Compose is not available. Please install Docker Compose."
        exit 1
    fi
    
    print_success "Docker Compose is available: $COMPOSE_CMD"
}

# Create necessary directories
create_directories() {
    print_info "Creating necessary directories..."
    
    mkdir -p data/uploads
    mkdir -p data/sessions
    mkdir -p data/logs
    
    print_success "Directories created"
}

# Build the Docker image
build_image() {
    print_info "Building Docker image..."
    
    if docker build -t $IMAGE_NAME:latest .; then
        print_success "Docker image built successfully"
    else
        print_error "Failed to build Docker image"
        exit 1
    fi
}

# Start the application
start_app() {
    print_info "Starting $APP_NAME..."
    
    if $COMPOSE_CMD up -d; then
        print_success "Application started successfully"
        print_info "Application is running at: http://localhost:$DEFAULT_PORT"
        print_info "Container name: $CONTAINER_NAME"
    else
        print_error "Failed to start application"
        exit 1
    fi
}

# Stop the application
stop_app() {
    print_info "Stopping $APP_NAME..."
    
    if $COMPOSE_CMD down; then
        print_success "Application stopped successfully"
    else
        print_error "Failed to stop application"
        exit 1
    fi
}

# Show application status
show_status() {
    print_info "Application Status:"
    echo ""
    
    # Check if container is running
    if docker ps --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -q $CONTAINER_NAME; then
        print_success "Container is running"
        docker ps --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    else
        print_warning "Container is not running"
    fi
    
    echo ""
    print_info "Recent logs:"
    docker logs --tail 10 $CONTAINER_NAME 2>/dev/null || print_warning "No logs available"
}

# Show logs
show_logs() {
    print_info "Showing logs for $APP_NAME..."
    docker logs -f $CONTAINER_NAME
}

# Clean up (remove containers and images)
cleanup() {
    print_warning "This will remove all containers and images for $APP_NAME"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "Cleaning up..."
        
        # Stop and remove containers
        $COMPOSE_CMD down --rmi all --volumes --remove-orphans
        
        # Remove dangling images
        docker image prune -f
        
        print_success "Cleanup completed"
    else
        print_info "Cleanup cancelled"
    fi
}

# Show help
show_help() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  build     Build the Docker image"
    echo "  start     Start the application"
    echo "  stop      Stop the application"
    echo "  restart   Restart the application"
    echo "  status    Show application status"
    echo "  logs      Show application logs"
    echo "  cleanup   Remove all containers and images"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 build     # Build the image"
    echo "  $0 start     # Start the application"
    echo "  $0 status    # Check if running"
    echo "  $0 logs      # View logs"
}

# Main script
main() {
    print_header
    
    # Check prerequisites
    check_docker
    check_docker_compose
    
    # Handle commands
    case "${1:-help}" in
        build)
            create_directories
            build_image
            ;;
        start)
            create_directories
            start_app
            ;;
        stop)
            stop_app
            ;;
        restart)
            stop_app
            sleep 2
            start_app
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs
            ;;
        cleanup)
            cleanup
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_error "Unknown command: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
