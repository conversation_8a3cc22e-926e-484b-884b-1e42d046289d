"""
DVV - FastAPI Web Application
Author: <PERSON> <<EMAIL>>
Description: A professional web application for validating PQ (Picture Quality) configuration files.
            Combines pattern-based parameter detection with external Dolby Vision tool validation
            to provide comprehensive configuration analysis and validation.
Version: 1.0.0
Copyright: (c) 2025 Dolby Laboratories, Inc.
"""

import os
import uuid
import json
import asyncio
import subprocess
import re
import warnings
import logging
import csv
import io
from pathlib import Path
from typing import List, Dict, Optional
from datetime import datetime, timedelta

from fastapi import FastAPI, Request, Form, File, UploadFile, HTTPException, Depends, Cookie, Query
from fastapi.responses import HTMLResponse, JSONResponse, RedirectResponse, StreamingResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# Import local modules
from exceptions import *
from web_app_registry import web_app_registry
from log_processors import LogProcessorMana<PERSON>, LogProcessingResult, TCFlashLogDetector, FirmwareLogDetector

# Delay config import to allow environment variables to be set first
def get_config():
    """Get configuration, allowing for dynamic loading"""
    import importlib
    import config
    # Reload config module to pick up any environment variables set after initial import
    importlib.reload(config)
    return config

# Initialize config and logging
config = get_config()
from utils import *

# Setup logging
setup_logging(config.LOG_LEVEL, config.LOGS_DIR)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="DVV",
    description="A Web app running Dolby Vision validators",
    version=config.APP_VERSION,
    debug=config.DEBUG,
    contact={
        "name": "Ethan Li",
        "email": "<EMAIL>"
    },
    license_info={
        "name": "Proprietary",
        "url": "https://www.dolby.com"
    }
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=config.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create directories if they don't exist
# Validate and create upload directory with proper error handling
try:
    config.UPLOAD_DIR = validate_upload_directory(config.UPLOAD_DIR)
    logger.info(f"Upload directory configured: {config.UPLOAD_DIR}")
except Exception as e:
    logger.error(f"Failed to setup upload directory: {str(e)}")
    raise

create_safe_directory(config.STATIC_DIR)
create_safe_directory(config.TEMPLATES_DIR)
create_safe_directory(config.DATA_DIR)
create_safe_directory(config.LOGS_DIR)

SESSIONS_FILE = config.DATA_DIR / "sessions.json"

# Mount static files
app.mount("/static", StaticFiles(directory=config.STATIC_DIR), name="static")

# Ensure log plots directory exists and mount it
config.LOG_PLOTS_DIR.mkdir(parents=True, exist_ok=True)
app.mount("/plots", StaticFiles(directory=config.LOG_PLOTS_DIR), name="plots")

# Templates
templates = Jinja2Templates(directory=config.TEMPLATES_DIR)

# Session storage
class SessionManager:
    def __init__(self):
        self.sessions: Dict[str, Dict] = {}
        self.load_sessions()

    def load_sessions(self):
        """Load sessions from file"""
        if SESSIONS_FILE.exists():
            try:
                with open(SESSIONS_FILE, 'r') as f:
                    data = json.load(f)
                    # Convert string dates back to datetime objects
                    for session_id, session_data in data.items():
                        if 'created_at' in session_data:
                            session_data['created_at'] = datetime.fromisoformat(session_data['created_at'])
                        if 'last_activity' in session_data:
                            session_data['last_activity'] = datetime.fromisoformat(session_data['last_activity'])
                    self.sessions = data
            except (json.JSONDecodeError, ValueError) as e:
                print(f"Error loading sessions: {e}")
                self.sessions = {}

    def save_sessions(self):
        """Save sessions to file"""
        try:
            # Convert datetime objects to strings for JSON serialization
            data = {}
            for session_id, session_data in self.sessions.items():
                data[session_id] = session_data.copy()
                if 'created_at' in data[session_id]:
                    data[session_id]['created_at'] = data[session_id]['created_at'].isoformat()
                if 'last_activity' in data[session_id]:
                    data[session_id]['last_activity'] = data[session_id]['last_activity'].isoformat()

            with open(SESSIONS_FILE, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            print(f"Error saving sessions: {e}")

    def create_session(self, user_id: str, selected_app: str = "pq_config_validator") -> str:
        """Create a new session"""
        session_id = str(uuid.uuid4())
        self.sessions[session_id] = {
            "user_id": user_id,
            "selected_app": selected_app,
            "created_at": datetime.now(),
            "last_activity": datetime.now(),
            "files": []
        }
        self.save_sessions()
        return session_id

    def get_session(self, session_id: str) -> Optional[Dict]:
        """Get session by ID"""
        session = self.sessions.get(session_id)
        if session:
            # Update last activity
            session['last_activity'] = datetime.now()
            self.save_sessions()
        return session

    def cleanup_expired_sessions(self, max_age_hours: int = 24):
        """Remove sessions older than max_age_hours"""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        expired_sessions = [
            session_id for session_id, session_data in self.sessions.items()
            if session_data.get('last_activity', datetime.min) < cutoff_time
        ]

        for session_id in expired_sessions:
            del self.sessions[session_id]

        if expired_sessions:
            self.save_sessions()

        return len(expired_sessions)

# Initialize session manager
session_manager = SessionManager()

# Pydantic models
class UserRegistration(BaseModel):
    user_id: str
    selected_app: Optional[str] = "pq_config_validator"

class ParameterResult(BaseModel):
    parameter_name: str
    line_number: int
    line_content: str

class ValidationWarning(BaseModel):
    warning_type: str
    message: str
    parameter: Optional[str] = None
    value: Optional[str] = None
    suggested_range: Optional[str] = None

class ProcessingResult(BaseModel):
    success: bool
    message: str
    undefined_parameters: List[ParameterResult] = []
    validation_warnings: List[ValidationWarning] = []
    external_tool_used: bool = False
    file_path: str = ""
    # Log processing specific fields
    processing_mode: str = "parameter_validation"  # "parameter_validation" or "log_visualization"
    processor_used: Optional[str] = None
    output_files: List[str] = []  # Generated image files for log processing
    static_files: List[Dict[str, str]] = []  # Static files with URLs for web display
    log_info: Optional[Dict] = None  # Additional log file information

# Utility functions
def create_user_directory(user_id: str) -> Path:
    """Create a directory for the user if it doesn't exist"""
    sanitized_user_id = sanitize_filename(user_id)
    user_dir = config.UPLOAD_DIR / sanitized_user_id
    return create_safe_directory(user_dir)

def validate_user_id(user_id: str) -> bool:
    """Validate user ID format"""
    return validate_user_id_format(user_id)

# Parameter detection functions (adapted from existing cfg_parser.py)
def find_placeholders(text: str) -> List[str]:
    """
    Finds and returns all placeholders within a given string.
    Looks for patterns like {parameter_name}
    """
    pattern = r'\{([^}]+)\}'
    return re.findall(pattern, text)

def find_parameters_in_line(line: str) -> List[str]:
    """
    Find parameter references in a line of text.
    This function looks for various parameter patterns commonly used in config files.
    """
    parameters = []

    # Pattern 1: {parameter_name} - curly braces
    parameters.extend(find_placeholders(line))

    # Pattern 2: ${parameter_name} - shell-style variables
    shell_pattern = r'\$\{([^}]+)\}'
    parameters.extend(re.findall(shell_pattern, line))

    # Pattern 3: $parameter_name - simple shell variables
    simple_shell_pattern = r'\$([a-zA-Z_][a-zA-Z0-9_]*)'
    parameters.extend(re.findall(simple_shell_pattern, line))

    # Pattern 4: %parameter_name% - Windows-style variables
    windows_pattern = r'%([a-zA-Z_][a-zA-Z0-9_]*)%'
    parameters.extend(re.findall(windows_pattern, line))

    # Pattern 5: @parameter_name@ - autotools-style variables
    autotools_pattern = r'@([a-zA-Z_][a-zA-Z0-9_]*)@'
    parameters.extend(re.findall(autotools_pattern, line))

    return list(set(parameters))  # Remove duplicates

def extract_defined_parameters(content: str) -> set:
    """
    Extract parameter definitions from the file content.
    This looks for common parameter definition patterns.
    """
    defined_params = set()
    lines = content.split('\n')

    for line in lines:
        line = line.strip()
        if not line or line.startswith('#') or line.startswith('//'):
            continue

        # Pattern 1: key = value or key: value
        assignment_pattern = r'^([a-zA-Z_][a-zA-Z0-9_]*)\s*[=:]\s*'
        match = re.match(assignment_pattern, line)
        if match:
            defined_params.add(match.group(1))

        # Pattern 2: JSON-style definitions
        json_pattern = r'"([a-zA-Z_][a-zA-Z0-9_]*)"\s*:'
        matches = re.findall(json_pattern, line)
        defined_params.update(matches)

        # Pattern 3: XML-style attributes
        xml_pattern = r'([a-zA-Z_][a-zA-Z0-9_]*)\s*='
        matches = re.findall(xml_pattern, line)
        defined_params.update(matches)

    return defined_params

def parse_dtv_tool_output(output: str) -> List[ValidationWarning]:
    """
    Parse output from the Dolby Vision configuration tool to extract warnings.
    """
    warnings = []
    lines = output.strip().split('\n')

    for line in lines:
        line = line.strip()
        if line.startswith('WARN:'):
            # Parse warning message
            warning_text = line[5:].strip()  # Remove 'WARN:' prefix

            # Try to extract specific warning patterns
            if 'is outside of the supported range' in warning_text:
                # Pattern: "Bx color primary (0.154700) is outside of the supported range. Clipped to [0.123047, 0.154297]"
                import re
                match = re.search(r'(\w+(?:\s+\w+)*)\s*\(([^)]+)\)\s*is outside of the supported range.*?Clipped to \[([^\]]+)\]', warning_text)
                if match:
                    parameter = match.group(1)
                    value = match.group(2)
                    suggested_range = match.group(3)
                    warnings.append(ValidationWarning(
                        warning_type="range_violation",
                        message=warning_text,
                        parameter=parameter,
                        value=value,
                        suggested_range=suggested_range
                    ))
                else:
                    # Fallback for unmatched range warnings
                    warnings.append(ValidationWarning(
                        warning_type="range_violation",
                        message=warning_text
                    ))
            else:
                # Generic warning
                warnings.append(ValidationWarning(
                    warning_type="validation_warning",
                    message=warning_text
                ))
        elif line.startswith('ERROR:'):
            # Parse error messages as warnings (since they're validation issues)
            error_text = line[6:].strip()  # Remove 'ERROR:' prefix
            warnings.append(ValidationWarning(
                warning_type="validation_error",
                message=error_text
            ))

    return warnings

def deduplicate_validation_warnings(warnings: List[ValidationWarning]) -> List[ValidationWarning]:
    """
    Remove duplicate validation warnings while preserving the most complete information.
    Deduplication is based on warning message content and parameter names.
    """
    if not warnings:
        return warnings

    # Dictionary to store unique warnings by a composite key
    unique_warnings = {}

    for warning in warnings:
        # Create a composite key for deduplication
        # Use message content and parameter (if available) to identify duplicates
        key_parts = []

        # Normalize the message for comparison (remove extra whitespace, case insensitive)
        normalized_message = ' '.join(warning.message.lower().split())
        key_parts.append(normalized_message)

        # Add parameter to key if available
        if warning.parameter:
            key_parts.append(warning.parameter.lower().strip())

        # Add warning type to key
        key_parts.append(warning.warning_type)

        composite_key = '|'.join(key_parts)

        # If this is a new warning or a more complete version of an existing one
        if composite_key not in unique_warnings:
            unique_warnings[composite_key] = warning
        else:
            # Keep the warning with more complete information
            existing = unique_warnings[composite_key]
            current = warning

            # Prefer warnings with more fields filled
            existing_fields = sum([
                bool(existing.parameter),
                bool(existing.value),
                bool(existing.suggested_range),
                len(existing.message)
            ])

            current_fields = sum([
                bool(current.parameter),
                bool(current.value),
                bool(current.suggested_range),
                len(current.message)
            ])

            # Replace if current warning has more information
            if current_fields > existing_fields:
                unique_warnings[composite_key] = current

    # Convert back to list and maintain original order as much as possible
    deduplicated = list(unique_warnings.values())

    # Sort by warning type and then by message for consistent output
    deduplicated.sort(key=lambda w: (w.warning_type, w.message))

    return deduplicated

async def run_external_validation_tool(file_path: Path) -> tuple[bool, List[ValidationWarning], str]:
    """
    Run the external Dolby Vision configuration validation tool.
    Returns: (success, warnings, output)
    """
    tool_path = Path("bin/dtv_config_lib_test.exe")

    # Check if tool exists
    if not tool_path.exists():
        logger.warning(f"External validation tool not found at {tool_path}")
        return False, [], "External validation tool not available"

    try:
        # Construct command
        cmd = [
            str(tool_path),
            "-t", str(file_path),
            "-b", str(file_path.with_suffix('.bin'))
        ]

        logger.info(f"Running external validation: {' '.join(cmd)}")

        # Run the tool
        result = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.STDOUT,
            cwd=Path.cwd()
        )

        stdout, _ = await result.communicate()
        output = stdout.decode('utf-8', errors='ignore')

        logger.debug(f"External tool output: {output}")

        # Parse warnings from output
        warnings = parse_dtv_tool_output(output)

        # Deduplicate warnings to remove duplicates from external tool
        warnings = deduplicate_validation_warnings(warnings)

        logger.info(f"External tool found {len(warnings)} unique validation warnings (after deduplication)")

        # Tool returns 0 on success, but may still have warnings
        success = result.returncode == 0

        return success, warnings, output

    except Exception as e:
        logger.error(f"Error running external validation tool: {str(e)}")
        return False, [], f"Error running external tool: {str(e)}"

async def process_file_for_parameters(file_path: Path, user_id: str) -> ProcessingResult:
    """
    Process a file to detect undefined parameters and run external validation.
    This function performs both pattern-based parameter detection and external tool validation.
    """
    try:
        logger.info(f"Processing file {file_path} for user {user_id}")

        # Read file content safely
        content = safe_file_read(file_path, config.MAX_FILE_SIZE)
        lines = content.split('\n')
        defined_parameters = extract_defined_parameters(content)
        undefined_parameters = []
        validation_warnings = []
        external_tool_used = False

        logger.debug(f"Found {len(defined_parameters)} defined parameters")

        # Phase 1: Pattern-based parameter detection
        for line_num, line in enumerate(lines, 1):
            # Validate line length
            line = validate_line_length(line, line_num)

            # Skip comments and empty lines
            if is_comment_line(line):
                continue

            parameters_in_line = find_parameters_in_line(line)

            for param in parameters_in_line:
                if param not in defined_parameters:
                    # Check if this parameter is already in our results
                    existing = next((p for p in undefined_parameters if p.parameter_name == param), None)
                    if not existing:
                        undefined_parameters.append(ParameterResult(
                            parameter_name=param,
                            line_number=line_num,
                            line_content=line.strip()
                        ))

        # Sort by line number
        undefined_parameters.sort(key=lambda x: x.line_number)

        # Phase 2: External tool validation (for supported file types)
        file_extension = file_path.suffix.lower()
        if file_extension in ['.cfg', '.txt']:
            logger.info("Running external Dolby Vision validation tool")
            tool_success, warnings, tool_output = await run_external_validation_tool(file_path)

            if tool_success or warnings:  # Even if tool fails, we might have captured warnings
                validation_warnings = warnings
                external_tool_used = True
                logger.info(f"External tool found {len(warnings)} validation warnings")
            else:
                logger.warning("External validation tool failed or not available")

        # Prepare result message
        param_count = len(undefined_parameters)
        warning_count = len(validation_warnings)

        if param_count > 0 and warning_count > 0:
            message = f"Analysis complete. Found {param_count} undefined parameter(s) and {warning_count} validation warning(s)."
        elif param_count > 0:
            message = f"Analysis complete. Found {param_count} undefined parameter(s)."
        elif warning_count > 0:
            message = f"Analysis complete. Found {warning_count} validation warning(s)."
        else:
            message = "Analysis complete. No issues found."

        logger.info(message)

        return ProcessingResult(
            success=True,
            message=message,
            undefined_parameters=undefined_parameters,
            validation_warnings=validation_warnings,
            external_tool_used=external_tool_used,
            file_path=str(file_path)
        )

    except (FileProcessingError, ValidationError) as e:
        logger.error(f"File processing error: {str(e)}")
        return ProcessingResult(
            success=False,
            message=str(e),
            undefined_parameters=[],
            validation_warnings=[],
            external_tool_used=False,
            file_path=str(file_path)
        )
    except Exception as e:
        logger.error(f"Unexpected error processing file: {str(e)}")
        return ProcessingResult(
            success=False,
            message=f"Unexpected error processing file: {str(e)}",
            undefined_parameters=[],
            validation_warnings=[],
            external_tool_used=False,
            file_path=str(file_path)
        )

async def process_log_file(file_path: Path, user_id: str, selected_app: str) -> ProcessingResult:
    """
    Process a TC Flash PQ log file for visualization using the selected application.

    Args:
        file_path: Path to the log file
        user_id: User ID for directory organization
        selected_app: Selected web application from dropdown (e.g., 'grid_comparison_plotter', 'log_parser_plotter')

    Returns:
        ProcessingResult: Result of the log processing operation
    """
    try:
        logger.info(f"Processing log file {file_path} for user {user_id}")

        # Check if log processing is enabled
        if not LOG_PROCESSING_ENABLED:
            return ProcessingResult(
                success=False,
                message="Log processing is currently disabled",
                processing_mode="log_visualization",
                file_path=str(file_path)
            )

        # Validate file based on selected application
        if selected_app == "grid_comparison_plotter":
            # Grid comparison requires TC Flash log format
            if not TCFlashLogDetector.is_tc_flash_log(file_path):
                return ProcessingResult(
                    success=False,
                    message="IDK-IC In-and-Out requires a TC Flash PQ log file " \
                    "with Config and DM values sections",
                    processing_mode="log_visualization",
                    file_path=str(file_path)
                )
        elif selected_app == "firmware_log_visualizer":
            # Firmware log visualizer requires a valid firmware log with 
            # extractable parameters
            if not FirmwareLogDetector.is_firmware_log(file_path):
                return ProcessingResult(
                    success=False,
                    message="Firmware Log Visualizer requires a valid firmware " \
                    "log file with extractable parameters",
                    processing_mode="log_visualization",
                    file_path=str(file_path)
                )
        else:
            # For other applications, default to TC Flash log validation
            if not TCFlashLogDetector.is_tc_flash_log(file_path):
                return ProcessingResult(
                    success=False,
                    message="File is not a supported TC Flash PQ log file",
                    processing_mode="log_visualization",
                    file_path=str(file_path)
                )

        # Map selected web application to processor type
        app_to_processor = {
            "grid_comparison_plotter": "grid_comparison",
            "firmware_log_visualizer": "firmware_log_visualizer"
        }

        processor_type = app_to_processor.get(selected_app)
        if not processor_type:
            return ProcessingResult(
                success=False,
                message=f"Unknown web application selected: {selected_app}",
                processing_mode="log_visualization",
                file_path=str(file_path)
            )

        # Create output directory for this user and session
        user_dir = create_user_directory(user_id)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = user_dir / config.LOG_OUTPUT_DIR / timestamp

        # Process the log file with the selected processor
        log_result = LogProcessorManager.process_log_file(file_path, output_dir, processor_type)

        if not log_result.success:
            return ProcessingResult(
                success=False,
                message=log_result.message,
                processing_mode="log_visualization",
                processor_used=log_result.processor_used,
                file_path=str(file_path)
            )

        # Get log info for the result
        log_info = TCFlashLogDetector.get_log_info(file_path)

        return ProcessingResult(
            success=True,
            message=log_result.message,
            processing_mode="log_visualization",
            processor_used=log_result.processor_used,
            output_files=log_result.output_files,
            static_files=log_result.static_files,  # Use static files from LogProcessingResult
            file_path=str(file_path),
            log_info=log_info
        )

    except Exception as e:
        logger.error(f"Unexpected error processing log file: {str(e)}")
        return ProcessingResult(
            success=False,
            message=f"Unexpected error processing log file: {str(e)}",
            processing_mode="log_visualization",
            file_path=str(file_path)
        )

# Routes
@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """Home page - user ID registration"""
    available_apps = web_app_registry.get_enabled_applications()
    return templates.TemplateResponse("index.html", {
        "request": request,
        "available_apps": available_apps,
        "timestamp": int(datetime.now().timestamp())
    })

@app.get("/api/web-applications")
async def get_web_applications():
    """Get list of available web applications"""
    apps = web_app_registry.get_enabled_applications()
    return {
        "applications": [
            {
                "id": app.id,
                "name": app.name,
                "description": app.description,
                "icon": app.icon,
                "url": app.url,
                "author": app.author,
                "version": app.version
            }
            for app in apps
        ]
    }

@app.post("/register")
async def register_user(user_registration: UserRegistration):
    """Register a new user ID"""
    user_id = user_registration.user_id.strip()
    selected_app = user_registration.selected_app or "pq_config_validator"

    if not validate_user_id(user_id):
        raise HTTPException(status_code=400, detail="Invalid user ID format")

    # Validate selected application
    if not web_app_registry.is_valid_application(selected_app):
        raise HTTPException(status_code=400, detail="Invalid web application selected")

    # Define integrated applications that use the same upload interface
    integrated_apps = ["pq_config_validator", "grid_comparison_plotter", "firmware_log_visualizer"]

    # For external apps (not integrated), redirect to their URL
    if selected_app not in integrated_apps:
        app_url = web_app_registry.get_application_url(selected_app, user_id)
        if app_url and app_url != "/":  # Don't redirect if URL is just "/"
            return {
                "redirect_url": app_url,
                "selected_app": selected_app,
                "message": f"Redirecting to {web_app_registry.get_application(selected_app).name}"
            }
        else:
            raise HTTPException(status_code=503, detail="Selected application is currently unavailable")

    # Create user session for integrated applications
    session_id = session_manager.create_session(user_id, selected_app)

    # Create user directory
    create_user_directory(user_id)

    return {
        "session_id": session_id,
        "user_id": user_id,
        "selected_app": selected_app,
        "message": "User registered successfully"
    }

@app.get("/upload/{session_id}", response_class=HTMLResponse)
async def upload_page(request: Request, session_id: str):
    """File upload page"""
    user_data = session_manager.get_session(session_id)
    if not user_data:
        raise HTTPException(status_code=404, detail="Session not found or expired")

    # Get selected application information
    selected_app_id = user_data.get("selected_app", "pq_config_validator")
    selected_app = web_app_registry.get_application(selected_app_id)

    if not selected_app:
        # Fallback to default if application not found
        selected_app = web_app_registry.get_application("pq_config_validator")

    return templates.TemplateResponse("upload.html", {
        "request": request,
        "user_id": user_data["user_id"],
        "session_id": session_id,
        "selected_app": selected_app,
        "selected_app_id": selected_app_id,
        "timestamp": int(datetime.now().timestamp())
    })

@app.get("/api/session/{session_id}")
async def get_session_info(session_id: str):
    """Get session information"""
    user_data = session_manager.get_session(session_id)
    if not user_data:
        raise HTTPException(status_code=404, detail="Session not found or expired")

    return {
        "user_id": user_data["user_id"],
        "selected_app": user_data.get("selected_app", "pq_config_validator"),
        "created_at": user_data["created_at"].isoformat(),
        "last_activity": user_data["last_activity"].isoformat(),
        "files_count": len(user_data.get("files", []))
    }

@app.post("/api/cleanup-sessions")
async def cleanup_sessions():
    """Cleanup expired sessions (admin endpoint)"""
    cleaned_count = session_manager.cleanup_expired_sessions()
    return {"message": f"Cleaned up {cleaned_count} expired sessions"}

@app.post("/api/upload/{session_id}")
async def upload_file(session_id: str, file: UploadFile = File(...)):
    """Upload and process a text file"""
    try:
        # Validate session
        user_data = session_manager.get_session(session_id)
        if not user_data:
            logger.warning(f"Upload attempt with invalid session: {session_id}")
            raise HTTPException(status_code=404, detail="Session not found or expired")

        # Validate file
        if not file.filename:
            raise HTTPException(status_code=400, detail="No file provided")

        # Sanitize filename
        original_filename = file.filename
        sanitized_filename = sanitize_filename(original_filename)

        # Check file extension
        file_extension = get_file_extension(sanitized_filename)
        if file_extension not in config.ALLOWED_EXTENSIONS:
            raise HTTPException(
                status_code=400,
                detail=f"File type not supported. Allowed: {', '.join(config.ALLOWED_EXTENSIONS)}"
            )

        # Read and validate file content
        content = await file.read()
        if len(content) > config.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=400,
                detail=f"File size {format_file_size(len(content))} exceeds maximum allowed size {format_file_size(config.MAX_FILE_SIZE)}"
            )

        logger.info(f"Processing upload: {original_filename} ({format_file_size(len(content))}) for user {user_data['user_id']}")

        # Create user directory
        user_dir = create_user_directory(user_data["user_id"])

        # Generate unique filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_filename = f"{timestamp}_{sanitized_filename}"
        file_path = user_dir / safe_filename

        # Save file
        with open(file_path, 'wb') as f:
            f.write(content)

        # Determine processing mode based on file content and selected application
        selected_app = user_data.get("selected_app", "pq_config_validator")

        # Check if user explicitly selected a log visualization application
        log_visualization_apps = ["grid_comparison_plotter", "firmware_log_visualizer"]

        if selected_app in log_visualization_apps:
            # User explicitly selected a log visualization app - use log processing
            result = await process_log_file(file_path, user_data["user_id"], selected_app)
        else:
            # Process for undefined parameters (PQ config validation mode)
            result = await process_file_for_parameters(file_path, user_data["user_id"])

        # Update session with file info
        file_info = {
            "filename": original_filename,
            "safe_filename": safe_filename,
            "upload_time": datetime.now().isoformat(),
            "file_size": len(content),
            "processed": result.success,
            "undefined_count": len(result.undefined_parameters) if result.success else 0
        }
        user_data["files"].append(file_info)
        session_manager.save_sessions()

        logger.info(f"File processing complete: {result.message}")
        return result

    except HTTPException:
        raise
    except (FileProcessingError, ValidationError) as e:
        logger.error(f"File processing error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error in file upload: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error during file processing")

@app.get("/api/files/{session_id}")
async def list_user_files(session_id: str):
    """List all files uploaded by the user"""
    user_data = session_manager.get_session(session_id)
    if not user_data:
        raise HTTPException(status_code=404, detail="Session not found or expired")

    return {"files": user_data.get("files", [])}

@app.delete("/api/files/{session_id}/{filename}")
async def delete_user_file(session_id: str, filename: str):
    """Delete a specific file"""
    user_data = session_manager.get_session(session_id)
    if not user_data:
        raise HTTPException(status_code=404, detail="Session not found or expired")

    try:
        user_dir = create_user_directory(user_data["user_id"])
        file_path = user_dir / filename

        if file_path.exists():
            file_path.unlink()

            # Remove from session files list
            user_data["files"] = [
                f for f in user_data.get("files", [])
                if f.get("safe_filename") != filename
            ]
            session_manager.save_sessions()

            return {"message": "File deleted successfully"}
        else:
            raise HTTPException(status_code=404, detail="File not found")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting file: {str(e)}")

def generate_json_report(result: ProcessingResult, user_id: str, filename: str) -> str:
    """Generate JSON format analysis report"""
    report = {
        "metadata": {
            "tool": "PQ Config Validator",
            "author": "Ethan Li",
            "version": "1.0.0",
            "timestamp": datetime.now().isoformat(),
            "user_id": user_id,
            "analyzed_file": filename,
            "external_tool_used": result.external_tool_used
        },
        "summary": {
            "success": result.success,
            "message": result.message,
            "undefined_parameters_count": len(result.undefined_parameters),
            "validation_warnings_count": len(result.validation_warnings)
        },
        "undefined_parameters": [
            {
                "parameter_name": param.parameter_name,
                "line_number": param.line_number,
                "line_content": param.line_content
            }
            for param in result.undefined_parameters
        ],
        "validation_warnings": [
            {
                "warning_type": warning.warning_type,
                "message": warning.message,
                "parameter": warning.parameter,
                "value": warning.value,
                "suggested_range": warning.suggested_range
            }
            for warning in result.validation_warnings
        ]
    }
    return json.dumps(report, indent=2)


def generate_html_report(result: ProcessingResult, user_id: str, filename: str) -> str:
    """Generate HTML format analysis report"""
    html = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PQ Config Validator Report</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 40px; background-color: #f8f9fa; }}
        .container {{ max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .header {{ text-align: center; border-bottom: 3px solid #007bff; padding-bottom: 20px; margin-bottom: 30px; }}
        .header h1 {{ color: #007bff; margin: 0; font-size: 2.5em; }}
        .header p {{ color: #6c757d; margin: 5px 0; }}
        .metadata {{ background: #e9ecef; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
        .metadata table {{ width: 100%; border-collapse: collapse; }}
        .metadata td {{ padding: 5px 10px; border-bottom: 1px solid #dee2e6; }}
        .metadata td:first-child {{ font-weight: bold; width: 150px; }}
        .summary {{ background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
        .summary.error {{ background: #f8d7da; border-color: #f5c6cb; }}
        .section {{ margin-bottom: 30px; }}
        .section h2 {{ color: #495057; border-bottom: 2px solid #dee2e6; padding-bottom: 10px; }}
        .parameter-item {{ background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin-bottom: 10px; border-radius: 5px; }}
        .warning-item {{ background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin-bottom: 10px; border-radius: 5px; }}
        .line-number {{ background: #007bff; color: white; padding: 2px 8px; border-radius: 3px; font-size: 0.9em; }}
        .code {{ font-family: 'Courier New', monospace; background: #f8f9fa; padding: 10px; border-left: 3px solid #007bff; margin-top: 10px; }}
        .footer {{ text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6; color: #6c757d; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>PQ Config Validator</h1>
            <p>Analysis Report by Ethan Li</p>
            <p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>

        <div class="metadata">
            <table>
                <tr><td>User ID:</td><td>{user_id}</td></tr>
                <tr><td>Analyzed File:</td><td>{filename}</td></tr>
                <tr><td>External Tool Used:</td><td>{'Yes' if result.external_tool_used else 'No'}</td></tr>
                <tr><td>Tool Version:</td><td>1.0.0</td></tr>
            </table>
        </div>

        <div class="summary {'error' if not result.success else ''}">
            <h3>Analysis Summary</h3>
            <p><strong>Status:</strong> {'Success' if result.success else 'Failed'}</p>
            <p><strong>Message:</strong> {result.message}</p>
            <p><strong>Undefined Parameters:</strong> {len(result.undefined_parameters)}</p>
            <p><strong>Validation Warnings:</strong> {len(result.validation_warnings)}</p>
        </div>"""

    # Undefined Parameters Section
    if result.undefined_parameters:
        html += """
        <div class="section">
            <h2>Undefined Parameters</h2>"""
        for i, param in enumerate(result.undefined_parameters, 1):
            html += f"""
            <div class="parameter-item">
                <h4>{i}. Parameter: <code>{param.parameter_name}</code>
                    <span class="line-number">Line {param.line_number}</span>
                </h4>
                <div class="code">{param.line_content}</div>
            </div>"""
        html += "</div>"

    # Validation Warnings Section
    if result.validation_warnings:
        html += """
        <div class="section">
            <h2>Validation Warnings</h2>"""
        for i, warning in enumerate(result.validation_warnings, 1):
            html += f"""
            <div class="warning-item">
                <h4>{i}. {warning.warning_type.replace('_', ' ').title()}</h4>
                <p><strong>Message:</strong> {warning.message}</p>"""
            if warning.parameter:
                html += f"<p><strong>Parameter:</strong> <code>{warning.parameter}</code></p>"
            if warning.value:
                html += f"<p><strong>Value:</strong> <code>{warning.value}</code></p>"
            if warning.suggested_range:
                html += f"<p><strong>Suggested Range:</strong> <code>{warning.suggested_range}</code></p>"
            html += "</div>"
        html += "</div>"

    html += """
        <div class="footer">
            <p>&copy; 2025 PQ Config Validator by Ethan Li - Dolby Laboratories</p>
        </div>
    </div>
</body>
</html>"""

    return html


@app.get("/api/download-results/{session_id}")
async def download_results(
    session_id: str,
    format: str = Query("html", pattern="^(html)$"),
    filename: str = Query(None)
):
    """Download analysis results in specified format"""
    user_data = session_manager.get_session(session_id)
    if not user_data:
        raise HTTPException(status_code=404, detail="Session not found or expired")

    # Get the most recent file analysis result
    files = user_data.get("files", [])
    if not files:
        raise HTTPException(status_code=404, detail="No analysis results found")

    # Use the most recent file or specified filename
    if filename:
        file_info = next((f for f in files if f.get("safe_filename") == filename), None)
        if not file_info:
            raise HTTPException(status_code=404, detail="Specified file not found")
    else:
        file_info = files[-1]  # Most recent file

    # Re-run analysis to get current results (in production, you might cache these)
    user_dir = create_user_directory(user_data["user_id"])
    file_path = user_dir / file_info["safe_filename"]

    if not file_path.exists():
        raise HTTPException(status_code=404, detail="Analysis file not found")

    try:
        # Re-analyze the file to get current results
        result = await process_file_for_parameters(file_path, user_data["user_id"])

        # Generate report based on format
        assert format == "html"  # only supports HTML
        content = generate_html_report(result, user_data["user_id"], file_info["filename"])
        media_type = "text/html"
        file_extension = "html"

        # Generate download filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        download_filename = f"pq_config_analysis_{user_data['user_id']}_{timestamp}.{file_extension}"

        # Create streaming response
        return StreamingResponse(
            io.BytesIO(content.encode('utf-8')),
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={download_filename}"}
        )

    except Exception as e:
        logger.error(f"Error generating download: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generating report: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    logger.info(f"Starting DVV v{config.APP_VERSION}")
    logger.info(f"Author: Ethan Li")
    logger.info(f"Server will be available at http://{config.HOST}:{config.PORT}")
    uvicorn.run(app, host=config.HOST, port=config.PORT, log_level=config.LOG_LEVEL.lower())
