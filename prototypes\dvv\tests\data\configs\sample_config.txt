# Sample Configuration File for Testing
# This file contains various parameter patterns to test the detection tool

# Defined parameters
server_host = localhost
server_port = 8080
database_name = myapp_db
api_key = abc123xyz
timeout_seconds = 30

# Configuration sections with undefined parameters
[server]
host = {server_host}
port = {server_port}
ssl_enabled = {ssl_enabled}  # This parameter is not defined
max_connections = {max_conn}  # This parameter is not defined

[database]
name = {database_name}
username = {db_user}  # This parameter is not defined
password = {db_password}  # This parameter is not defined
connection_timeout = {timeout_seconds}

[api]
key = {api_key}
base_url = {api_base_url}  # This parameter is not defined
version = {api_version}  # This parameter is not defined

# Shell-style variables
export PATH=${PATH}:${custom_path}  # custom_path is not defined
export HOME=${HOME}
export USER_CONFIG=${config_dir}/user.conf  # config_dir is not defined

# Windows-style variables
set TEMP_DIR=%TEMP%
set LOG_FILE=%log_directory%\app.log  # log_directory is not defined
set BACKUP_PATH=%backup_root%\%date%  # backup_root and date are not defined

# Simple shell variables
echo "Welcome $username"  # username is not defined
echo "Current directory: $PWD"
echo "Application path: $app_path"  # app_path is not defined

# Autotools-style variables
prefix = @prefix@
exec_prefix = @exec_prefix@  # This might be undefined
bindir = @bindir@
libdir = @custom_libdir@  # custom_libdir is not defined

# Mixed patterns in complex expressions
command = "start /D {work_dir} ${app_executable} --config=%config_file% @data_dir@/input.txt"
# work_dir, app_executable, config_file, and data_dir are not defined

# JSON-like structure
{
  "server": {
    "host": "{server_host}",
    "port": "{server_port}",
    "ssl": "{ssl_config}"  // ssl_config is not defined
  },
  "features": {
    "logging": "{enable_logging}",  // enable_logging is not defined
    "caching": "{cache_enabled}"    // cache_enabled is not defined
  }
}

# Comments should be ignored
# {this_is_in_comment} should not be detected
// {this_is_also_in_comment} should not be detected
/* {this_is_in_multiline_comment} should not be detected */
