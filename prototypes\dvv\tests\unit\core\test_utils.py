#!/usr/bin/env python3
"""
Unit tests for utils.py module

Tests utility functions including:
- Logging setup and configuration
- File handling utilities
- String processing utilities
- Validation helpers
"""

import pytest
import sys
from pathlib import Path
import tempfile
import logging

# Add parent directories to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent))

from utils import *


class TestUtilities:
    """Test utility functions"""
    
    def test_setup_logging(self):
        """Test logging setup functionality"""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_dir = Path(temp_dir)
            
            # Test that setup_logging function exists and can be called
            try:
                setup_logging("INFO", log_dir)
                # Verify logger was configured
                logger = logging.getLogger(__name__)
                assert logger is not None
            except Exception as e:
                pytest.fail(f"setup_logging failed: {e}")
    
    def test_utility_functions_exist(self):
        """Test that expected utility functions exist"""
        # This test ensures the utils module can be imported
        # and contains expected functionality
        import utils
        assert utils is not None


if __name__ == "__main__":
    pytest.main([__file__])
