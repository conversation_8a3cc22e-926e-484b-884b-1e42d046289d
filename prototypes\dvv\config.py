"""
Configuration settings for the PQ Config Validator
Author: <PERSON> <<EMAIL>>
"""

import os
from pathlib import Path

# Application settings
APP_NAME = "PQ Config Validator"
APP_VERSION = "1.0.0"
APP_AUTHOR = "Ethan Li"
APP_AUTHOR_EMAIL = "<EMAIL>"
DEBUG = os.getenv("DEBUG", "false").lower() == "true"

# Server settings
HOST = os.getenv("HOST", "0.0.0.0")
PORT = int(os.getenv("PORT", 8000))

# File upload settings
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
ALLOWED_EXTENSIONS = {'.txt', '.cfg'}

# Session settings
SESSION_TIMEOUT_HOURS = int(os.getenv("SESSION_TIMEOUT_HOURS", 24))
SESSION_CLEANUP_INTERVAL_MINUTES = int(os.getenv("SESSION_CLEANUP_INTERVAL_MINUTES", 60))

# Web Application Registry settings
WEB_APPLICATIONS = {
    "pq_config_validator": {
        "name": "PQ Config Validator",
        "description": "PQ (Picture Quality) configuration file validator",
        "icon": "fas fa-cogs",
        "url": "/",
        "enabled": True,
        "author": "Ethan Li",
        "version": "1.0.0"
    },
    "grid_comparison_plotter": {
        "name": "IDK-IC In-and-Out",
        "description": "IDK-IC log visualization with comprehensive grid layout comparison",
        "icon": "fas fa-th",
        "url": "/",
        "enabled": True,
        "author": "Ethan Li",
        "version": "1.0.0"
    },
    "firmware_log_visualizer": {
        "name": "MTK FW Log Visualizer",
        "description": "MTK firmware log visualization with comprehensive grid layout comparison",
        "icon": "fas fa-microchip",
        "url": "/",
        "enabled": True,
        "author": "Ethan Li",
        "version": "1.0.0"
    }
}

# Directory settings
BASE_DIR = Path(__file__).parent

# Upload directory - configurable via environment variable
UPLOAD_DIR_ENV = os.getenv("UPLOAD_DIR")
if UPLOAD_DIR_ENV:
    UPLOAD_DIR = Path(UPLOAD_DIR_ENV).resolve()
else:
    UPLOAD_DIR = BASE_DIR / "uploads"

# Note: Upload directory validation is performed in main.py during startup
# to provide better error messages and handle initialization properly

STATIC_DIR = BASE_DIR / "static"
TEMPLATES_DIR = BASE_DIR / "templates"
DATA_DIR = BASE_DIR / "data"
LOGS_DIR = BASE_DIR / "logs"

# Log processing settings
LOG_PROCESSING_ENABLED = os.getenv("LOG_PROCESSING_ENABLED", "true").lower() == "true"
LOG_OUTPUT_DIR = "log_outputs"
LOG_PLOTS_DIR = STATIC_DIR / "plots"

# Security settings
ALLOWED_HOSTS = os.getenv("ALLOWED_HOSTS", "*").split(",")
CORS_ORIGINS = os.getenv("CORS_ORIGINS", "*").split(",")

# Logging settings
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Parameter detection settings
PARAMETER_PATTERNS = {
    "curly_braces": r'\{([^}]+)\}',           # {parameter_name}
    "shell_style": r'\$\{([^}]+)\}',          # ${parameter_name}
    "simple_shell": r'\$([a-zA-Z_][a-zA-Z0-9_]*)',  # $parameter_name
    "windows_style": r'%([a-zA-Z_][a-zA-Z0-9_]*)%',  # %parameter_name%
    "autotools_style": r'@([a-zA-Z_][a-zA-Z0-9_]*)@'  # @parameter_name@
}

DEFINITION_PATTERNS = {
    "assignment": r'^([a-zA-Z_][a-zA-Z0-9_]*)\s*[=:]\s*',  # key = value or key: value
    "json_style": r'"([a-zA-Z_][a-zA-Z0-9_]*)"\s*:',       # "key":
    "xml_style": r'([a-zA-Z_][a-zA-Z0-9_]*)\s*='           # key=
}

# File processing settings
ENCODING_FALLBACKS = ['utf-8', 'utf-16', 'latin-1', 'cp1252']
MAX_LINE_LENGTH = 10000  # Maximum characters per line to process
IGNORE_COMMENT_PATTERNS = [r'^\s*#', r'^\s*//', r'^\s*/\*', r'^\s*\*', r'^\s*<!--']
