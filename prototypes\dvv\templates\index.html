{% extends "base.html" %}

{% block title %}Home - PQ Config Validator{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="card-title mb-0">
                    <i class="fas fa-apps me-2"></i>
                    Web Application Manager
                </h4>
            </div>
            <div class="card-body">
                <p class="card-text mb-4">
                    Multi-application web service platform by <strong>Ethan Li</strong>.
                    Select your preferred web application and enter a unique user ID to get started.
                </p>
                
                <form id="registrationForm">
                    <div class="mb-3">
                        <label for="webAppSelect" class="form-label">
                            <i class="fas fa-apps me-1"></i>
                            Select Web Application
                        </label>
                        <select class="form-select" id="webAppSelect" name="selected_app" required>
                            {% for app in available_apps %}
                            <option value="{{ app.id }}" {% if app.id == 'pq_config_validator' %}selected{% endif %}>
                                {{ app.name }} - {{ app.description }}
                            </option>
                            {% endfor %}
                        </select>
                        <div class="form-text">
                            Choose the web application you want to use.
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="userId" class="form-label">
                            <i class="fas fa-id-card me-1"></i>
                            User ID
                        </label>
                        <input
                            type="text"
                            class="form-control"
                            id="userId"
                            name="user_id"
                            placeholder="Enter your unique user ID (e.g., john_doe_2025)"
                            required
                            pattern="[a-zA-Z0-9\s\-_]+"
                            title="Only letters, numbers, spaces, hyphens, and underscores are allowed"
                        >
                        <div class="form-text">
                            Use only letters, numbers, spaces, hyphens, and underscores.
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-arrow-right me-2"></i>
                            Continue to File Upload
                        </button>
                    </div>
                </form>
                
                <div id="alertContainer" class="mt-3"></div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    How it works
                </h5>
            </div>
            <div class="card-body">
                <ol class="mb-0">
                    <li>Select your preferred web application from the dropdown</li>
                    <li>Enter a unique user ID to identify your session</li>
                    <li>You'll be redirected to the selected application</li>
                    <li>Follow the application-specific workflow for your tasks</li>
                </ol>
                <div class="mt-3">
                    <small class="text-muted">
                        <strong>Note:</strong> Your application selection will be remembered for future visits.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Load previously selected application from localStorage
document.addEventListener('DOMContentLoaded', function() {
    const savedApp = localStorage.getItem('selectedWebApp');
    if (savedApp) {
        const selectElement = document.getElementById('webAppSelect');
        if (selectElement) {
            selectElement.value = savedApp;
        }
    }
});

document.getElementById('registrationForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const userId = document.getElementById('userId').value.trim();
    const selectedApp = document.getElementById('webAppSelect').value;
    const alertContainer = document.getElementById('alertContainer');

    if (!userId) {
        showAlert('Please enter a user ID', 'danger');
        return;
    }

    if (!selectedApp) {
        showAlert('Please select a web application', 'danger');
        return;
    }

    // Store selection in localStorage for persistence
    localStorage.setItem('selectedWebApp', selectedApp);

    try {
        const response = await fetch('/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_id: userId,
                selected_app: selectedApp
            })
        });

        const data = await response.json();

        if (response.ok) {
            if (data.redirect_url) {
                // External application - redirect to their URL
                showAlert(`Redirecting to ${data.selected_app}...`, 'info');
                setTimeout(() => {
                    window.open(data.redirect_url, '_blank');
                }, 1500);
            } else {
                // PQ Config Validator - continue to upload page
                showAlert('Registration successful! Redirecting...', 'success');
                setTimeout(() => {
                    window.location.href = `/upload/${data.session_id}`;
                }, 1500);
            }
        } else {
            showAlert(data.detail || 'Registration failed', 'danger');
        }
    } catch (error) {
        showAlert('Network error. Please try again.', 'danger');
    }
});

function showAlert(message, type) {
    const alertContainer = document.getElementById('alertContainer');
    alertContainer.innerHTML = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
}
</script>
{% endblock %}
