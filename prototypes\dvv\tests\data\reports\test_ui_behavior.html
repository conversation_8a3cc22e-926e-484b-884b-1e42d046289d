<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI Behavior Test - Grid Comparison Plotter</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-case { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        .result { margin: 10px 0; padding: 10px; background-color: #f8f9fa; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>UI Behavior Test: Log Info Section Visibility</h1>

    <div class="test-case info">
        <h3>Test Purpose</h3>
        <p>This test verifies that the "Log File Information" section is hidden for Grid Comparison Plotter and Firmware Log Visualizer results, but shown for PQ Config Validator.</p>
    </div>

    <div class="test-case">
        <h3>Test Case 1: Grid Comparison Plotter (should hide log info)</h3>
        <div id="test1-result" class="result"></div>
    </div>

    <div class="test-case">
        <h3>Test Case 2: Firmware Log Visualizer (should hide log info)</h3>
        <div id="test2-result" class="result"></div>
    </div>

    <div class="test-case">
        <h3>Test Case 3: PQ Config Validator (should show log info)</h3>
        <div id="test3-result" class="result"></div>
    </div>

    <script>
        // Import the formatLogVisualizationResults function from app.js
        // Since we can't directly import, we'll copy the relevant function here for testing
        
        function formatLogVisualizationResults(results) {
            let html = '';

            // Header with processing information
            if (results.success) {
                html += `
                    <div class="alert alert-success">
                        <i class="fas fa-chart-line me-2"></i>
                        <strong>Log Processing Complete!</strong> ${results.message}
                        <br><small>Processor used: ${results.processor_used || 'auto-detected'}</small>
                    </div>
                `;
            } else {
                html += `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Processing Failed:</strong> ${results.message}
                        ${results.processor_used ? `<br><small>Attempted processor: ${results.processor_used}</small>` : ''}
                    </div>
                `;
                return html;
            }

            // Display log file information (skip for Grid Comparison Plotter and Firmware Log Visualizer)
            if (results.log_info && results.processor_used !== 'grid_comparison' && results.processor_used !== 'firmware_log_visualizer') {
                html += `
                    <div class="card mb-3">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>Log File Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                `;

                const info = results.log_info.additional_info || {};
                if (info.total_frames) {
                    html += `
                                <div class="col-md-3">
                                    <strong>Total Frames:</strong><br>
                                    <span class="badge bg-primary">${info.total_frames}</span>
                                </div>
                    `;
                }
                if (info.config_parameters !== undefined) {
                    html += `
                                <div class="col-md-3">
                                    <strong>Config Parameters:</strong><br>
                                    <span class="badge bg-info">${info.config_parameters}</span>
                                </div>
                    `;
                }
                if (info.dm_parameters !== undefined) {
                    html += `
                                <div class="col-md-3">
                                    <strong>DM Parameters:</strong><br>
                                    <span class="badge bg-info">${info.dm_parameters}</span>
                                </div>
                    `;
                }
                if (info.total_parameters) {
                    html += `
                                <div class="col-md-3">
                                    <strong>Total Parameters:</strong><br>
                                    <span class="badge bg-success">${info.total_parameters}</span>
                                </div>
                    `;
                }

                html += `
                            </div>
                        </div>
                    </div>
                `;
            }

            // Add visualization section (simplified for test)
            html += `
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-images me-2"></i>Generated Visualizations
                        </h5>
                    </div>
                    <div class="card-body">
                        <p>Visualization content would appear here...</p>
                    </div>
                </div>
            `;

            return html;
        }

        // Test Case 1: Grid Comparison Plotter
        const gridComparisonResult = {
            success: true,
            message: "Grid comparison visualization generated successfully",
            processor_used: "grid_comparison",
            log_info: {
                file_type: "tc_flash_log",
                additional_info: {
                    total_frames: 2,
                    config_parameters: 4,
                    dm_parameters: 2,
                    total_parameters: 6
                }
            },
            static_files: [{
                filename: "all_parameters_comparison_grid.png",
                unique_filename: "test_grid_comparison.png",
                url: "/static/images/grid_comparison/test_grid_comparison.png"
            }]
        };

        // Test Case 2: Firmware Log Visualizer
        const firmwareVisualizerResult = {
            success: true,
            message: "Firmware log visualization generated successfully",
            processor_used: "firmware_log_visualizer",
            log_info: {
                file_type: "firmware_log",
                additional_info: {
                    total_parameters: 4,
                    file_size: 256
                }
            },
            static_files: [{
                filename: "L17-IC-filter_0.0416.png",
                unique_filename: "test_firmware_viz.png",
                url: "/static/images/firmware_log_visualizer/test_firmware_viz.png"
            }]
        };

        // Test Case 3: PQ Config Validator (should show log info)
        const pqConfigValidatorResult = {
            success: true,
            message: "Parameter validation completed successfully",
            processor_used: "pq_config_validator",
            log_info: {
                file_type: "config_file",
                additional_info: {
                    total_parameters: 25,
                    undefined_parameters: 3,
                    file_size: 1024
                }
            }
        };

        // Run tests
        function runTests() {
            // Test 1: Grid Comparison Plotter
            const test1Html = formatLogVisualizationResults(gridComparisonResult);
            const test1HasLogInfo = test1Html.includes('Log File Information');
            
            document.getElementById('test1-result').innerHTML = `
                <strong>Result:</strong> ${test1HasLogInfo ? '❌ FAIL' : '✅ PASS'}<br>
                <strong>Expected:</strong> Log File Information section should be hidden<br>
                <strong>Actual:</strong> Log File Information section is ${test1HasLogInfo ? 'visible' : 'hidden'}<br>
                <details>
                    <summary>Generated HTML (click to expand)</summary>
                    <pre style="background: #f5f5f5; padding: 10px; margin: 10px 0; overflow-x: auto;">${test1Html.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</pre>
                </details>
            `;
            
            if (!test1HasLogInfo) {
                document.getElementById('test1-result').parentElement.classList.add('success');
            }

            // Test 2: Firmware Log Visualizer
            const test2Html = formatLogVisualizationResults(firmwareVisualizerResult);
            const test2HasLogInfo = test2Html.includes('Log File Information');

            document.getElementById('test2-result').innerHTML = `
                <strong>Result:</strong> ${test2HasLogInfo ? '❌ FAIL' : '✅ PASS'}<br>
                <strong>Expected:</strong> Log File Information section should be hidden<br>
                <strong>Actual:</strong> Log File Information section is ${test2HasLogInfo ? 'visible' : 'hidden'}<br>
                <details>
                    <summary>Generated HTML (click to expand)</summary>
                    <pre style="background: #f5f5f5; padding: 10px; margin: 10px 0; overflow-x: auto;">${test2Html.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</pre>
                </details>
            `;

            if (!test2HasLogInfo) {
                document.getElementById('test2-result').parentElement.classList.add('success');
            }

            // Test 3: PQ Config Validator
            const test3Html = formatLogVisualizationResults(pqConfigValidatorResult);
            const test3HasLogInfo = test3Html.includes('Log File Information');

            document.getElementById('test3-result').innerHTML = `
                <strong>Result:</strong> ${test3HasLogInfo ? '✅ PASS' : '❌ FAIL'}<br>
                <strong>Expected:</strong> Log File Information section should be visible<br>
                <strong>Actual:</strong> Log File Information section is ${test3HasLogInfo ? 'visible' : 'hidden'}<br>
                <details>
                    <summary>Generated HTML (click to expand)</summary>
                    <pre style="background: #f5f5f5; padding: 10px; margin: 10px 0; overflow-x: auto;">${test3Html.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</pre>
                </details>
            `;

            if (test3HasLogInfo) {
                document.getElementById('test3-result').parentElement.classList.add('success');
            }
        }

        // Run tests when page loads
        window.onload = runTests;
    </script>
</body>
</html>
