# Docker Installation Guide for Windows

## Quick Installation Steps

### Option 1: Docker Desktop (Recommended)

1. **Download Docker Desktop:**
   - Visit: https://www.docker.com/products/docker-desktop/
   - Click "Download for Windows"
   - File: ~500MB download

2. **Install Docker Desktop:**
   - Run the downloaded installer
   - Follow the installation wizard
   - **Important:** Enable WSL 2 integration when prompted
   - Restart your computer when installation completes

3. **Verify Installation:**
   ```cmd
   docker --version
   docker-compose --version
   ```

4. **Start Docker Desktop:**
   - Launch Docker Desktop from Start Menu
   - Wait for Docker to start (whale icon in system tray)
   - Sign in or skip account creation

### Option 2: Alternative - Use Existing Python Environment

If you prefer not to install Docker right now, you can continue using the existing Python environment that's already working:

```cmd
# Current working setup (already tested)
python run.py --port 8000
```

## After Docker Installation

Once Docker is installed, return here and run:

```cmd
# Build and deploy with Docker
docker-deploy.bat build
docker-deploy.bat start

# Access application
# http://localhost:8000
```

## System Requirements

- **Windows 10/11** (64-bit)
- **4GB RAM** minimum (8GB recommended)
- **WSL 2** (will be installed automatically)
- **Virtualization** enabled in BIOS

## Troubleshooting

**If Docker fails to start:**
1. Enable Virtualization in BIOS
2. Enable WSL 2: `wsl --install`
3. Restart computer
4. Start Docker Desktop

**If port 8000 is busy:**
- Edit `docker-compose.yml`
- Change `"8000:8000"` to `"8080:8000"`
- Use http://localhost:8080

## Next Steps

After Docker installation:
1. ✅ Docker Desktop running
2. ✅ Run: `docker-deploy.bat build`
3. ✅ Run: `docker-deploy.bat start`
4. ✅ Test: http://localhost:8000
