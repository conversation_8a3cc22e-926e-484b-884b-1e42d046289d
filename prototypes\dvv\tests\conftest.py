"""
Shared test fixtures and configuration for web application selection tests
Author: <PERSON> <<EMAIL>>
"""

import sys
import tempfile
import json
from pathlib import Path
from datetime import datetime

# Add parent directory to path to import local modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from web_app_registry import WebApplicationRegistry, WebApplication
from main import SessionManager

class TestFixtures:
    """Shared test fixtures and utilities"""
    
    @staticmethod
    def create_test_registry():
        """Create a test web application registry with sample data"""
        registry = WebApplicationRegistry()
        
        # Clear existing applications
        registry.applications = {}
        
        # Add test applications
        test_apps = {
            "test_app_1": {
                "name": "Test App 1",
                "description": "First test application",
                "icon": "fas fa-test",
                "url": "/test1",
                "enabled": True,
                "author": "Test Author",
                "version": "1.0.0"
            },
            "test_app_2": {
                "name": "Test App 2",
                "description": "Second test application",
                "icon": "fas fa-test2",
                "url": "http://localhost:8000",
                "enabled": True,
                "author": "Test Author",
                "version": "2.0.0"
            },
            "disabled_app": {
                "name": "Disabled App",
                "description": "Disabled test application",
                "icon": "fas fa-disabled",
                "url": "/disabled",
                "enabled": False,
                "author": "Test Author",
                "version": "0.1.0"
            },
            "grid_comparison_plotter": {
                "name": "Grid Comparison Plotter",
                "description": "TC Flash PQ log analysis with comprehensive grid layout comparison plots",
                "icon": "fas fa-th",
                "url": "http://localhost:8000",
                "enabled": True,
                "author": "Ethan Li",
                "version": "1.0.0"
            },
            "log_parser_plotter": {
                "name": "Log Parser Plotter",
                "description": "TC Flash PQ log parser and time-series parameter visualization",
                "icon": "fas fa-chart-line",
                "url": "http://localhost:8000",
                "enabled": True,
                "author": "Ethan Li",
                "version": "1.0.0"
            }
        }
        
        for app_id, app_config in test_apps.items():
            registry.applications[app_id] = WebApplication(
                id=app_id,
                name=app_config["name"],
                description=app_config["description"],
                icon=app_config["icon"],
                url=app_config["url"],
                enabled=app_config["enabled"],
                author=app_config["author"],
                version=app_config["version"]
            )
        
        return registry
    
    @staticmethod
    def create_test_session_manager():
        """Create a test session manager with temporary storage"""
        # Create a temporary file for sessions
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        temp_file.close()
        
        # Create session manager with temporary file
        session_manager = SessionManager()
        session_manager.sessions_file = temp_file.name
        session_manager.sessions = {}
        
        return session_manager, temp_file.name
    
    @staticmethod
    def create_sample_session_data():
        """Create sample session data for testing"""
        return {
            "user_id": "test_user",
            "selected_app": "test_app_1",
            "created_at": datetime.now(),
            "last_activity": datetime.now(),
            "files": []
        }
    
    @staticmethod
    def create_test_user_registration():
        """Create test user registration data"""
        return {
            "user_id": "test_user_123",
            "selected_app": "test_app_1"
        }
    
    @staticmethod
    def cleanup_temp_file(file_path):
        """Clean up temporary files"""
        try:
            Path(file_path).unlink()
        except FileNotFoundError:
            pass

class TestValidators:
    """Test validation utilities"""
    
    @staticmethod
    def validate_web_application(app):
        """Validate a WebApplication object"""
        required_fields = ['id', 'name', 'description', 'icon', 'url', 'enabled', 'author', 'version']
        for field in required_fields:
            assert hasattr(app, field), f"WebApplication missing required field: {field}"
        
        assert isinstance(app.enabled, bool), "enabled field must be boolean"
        assert app.url.startswith(('/', 'http://', 'https://')), "url must be valid"
        assert len(app.name) > 0, "name cannot be empty"
        assert len(app.description) > 0, "description cannot be empty"
    
    @staticmethod
    def validate_session_data(session_data):
        """Validate session data structure"""
        required_fields = ['user_id', 'selected_app', 'created_at', 'last_activity', 'files']
        for field in required_fields:
            assert field in session_data, f"Session data missing required field: {field}"
        
        assert isinstance(session_data['files'], list), "files must be a list"
        assert len(session_data['user_id']) > 0, "user_id cannot be empty"
        assert len(session_data['selected_app']) > 0, "selected_app cannot be empty"
    
    @staticmethod
    def validate_api_response(response_data, expected_fields):
        """Validate API response structure"""
        for field in expected_fields:
            assert field in response_data, f"API response missing required field: {field}"

class TestConstants:
    """Test constants and configuration"""
    
    VALID_USER_IDS = [
        "test_user",
        "user_123",
        "john_doe_2025",
        "test-user",
        "test_user_with_underscores"
    ]
    
    INVALID_USER_IDS = [
        "",
        "   ",
        "<EMAIL>",
        "user#123",
        "user$special",
        "user%percent"
    ]
    
    VALID_APP_IDS = [
        "test_app_1",
        "test_app_2",
        "pq_config_validator"
    ]
    
    INVALID_APP_IDS = [
        "",
        "nonexistent_app",
        "disabled_app",
        None
    ]
