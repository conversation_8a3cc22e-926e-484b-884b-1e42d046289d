# PQ Config Validator - Quick Start Guide

**Author:** <PERSON> | **Version:** 1.0.0

## 🚀 Quick Deployment

### Prerequisites
- Docker installed and running
- Docker Compose available

### 1-Minute Setup

**Linux/macOS:**
```bash
chmod +x docker-deploy.sh
./docker-deploy.sh build
./docker-deploy.sh start
```

**Windows:**
```cmd
docker-deploy.bat build
docker-deploy.bat start
```

**Access:** http://localhost:8000

## 📋 Management Commands

| Action | Linux/macOS | Windows |
|--------|-------------|---------|
| Build | `./docker-deploy.sh build` | `docker-deploy.bat build` |
| Start | `./docker-deploy.sh start` | `docker-deploy.bat start` |
| Stop | `./docker-deploy.sh stop` | `docker-deploy.bat stop` |
| Status | `./docker-deploy.sh status` | `docker-deploy.bat status` |
| Logs | `./docker-deploy.sh logs` | `docker-deploy.bat logs` |
| Restart | `./docker-deploy.sh restart` | `docker-deploy.bat restart` |

## 🌐 Network Access

### LAN Access Setup
1. **Find your IP:** `ipconfig` (Windows) or `ifconfig` (Linux/macOS)
2. **Access URL:** `http://YOUR_IP:8000`
3. **Firewall:** Allow port 8000

### Port Configuration
Edit `docker-compose.yml`:
```yaml
ports:
  - "8080:8000"  # Change external port to 8080
```

## 📁 File Support

| Format | Description | Example |
|--------|-------------|---------|
| `.txt` | Text configuration files | `config.txt` |
| `.cfg` | Configuration files | `dolby.cfg` |

## 🔧 Features

- ✅ **Dual Format Support:** .txt and .cfg files
- ✅ **External Validation:** Dolby Vision tool integration
- ✅ **Report Downloads:** HTML and Markdown formats
- ✅ **Deduplication:** Removes duplicate warnings
- ✅ **English Interface:** All text in English
- ✅ **LAN Ready:** Network deployment support

## 📊 Data Persistence

**Volumes mounted:**
- `./data/uploads` - Uploaded files
- `./data/sessions` - User sessions  
- `./data/logs` - Application logs

## 🆘 Troubleshooting

**Port in use:**
```bash
# Check what's using port 8000
netstat -tulpn | grep 8000  # Linux
netstat -an | findstr 8000  # Windows
```

**Container won't start:**
```bash
docker logs pq-config-validator
```

**Reset everything:**
```bash
./docker-deploy.sh cleanup  # Linux/macOS
docker-deploy.bat cleanup   # Windows
```

## 📞 Support

**Author:** Ethan Li  
**Email:** <EMAIL>  
**Documentation:** See `DOCKER_DEPLOYMENT.md` for detailed guide
