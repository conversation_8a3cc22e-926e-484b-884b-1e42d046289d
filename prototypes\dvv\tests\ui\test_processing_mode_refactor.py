"""
Integration test for processing_mode refactoring

This test verifies that the refactoring from processor_used to processing_mode
works correctly in the JavaScript frontend and maintains backward compatibility.
"""

import pytest
import json
from pathlib import Path


class TestProcessingModeRefactor:
    """Test the refactoring from processor_used to processing_mode"""
    
    def test_javascript_refactoring_complete(self):
        """Test that JavaScript has been properly refactored"""
        
        js_file_path = Path(__file__).parent.parent.parent / "static/js/app.js"
        assert js_file_path.exists(), "JavaScript file not found"
        
        with open(js_file_path, 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        # Verify the old processor-specific logic is removed
        old_pattern_found = 'processor_used !== \'grid_comparison\' && results.processor_used !== \'firmware_log_visualizer\''
        assert old_pattern_found not in js_content, "Old processor-specific logic still present"
        
        # Verify the new processing_mode logic is present
        new_pattern = 'processing_mode === \'parameter_validation\''
        assert new_pattern in js_content, "New processing_mode logic not found"
        
        print("✓ JavaScript successfully refactored to use processing_mode")
    
    def test_processing_mode_values_consistency(self):
        """Test that processing_mode values are consistent across codebase"""
        
        # Check main.py for processing_mode values
        main_py_path = Path(__file__).parent.parent.parent / "main.py"
        with open(main_py_path, 'r', encoding='utf-8') as f:
            main_content = f.read()
        
        # Check JavaScript for processing_mode values
        js_file_path = Path(__file__).parent.parent.parent / "static/js/app.js"
        with open(js_file_path, 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        # Verify consistent values
        assert 'parameter_validation' in main_content, "parameter_validation not found in main.py"
        assert 'log_visualization' in main_content, "log_visualization not found in main.py"
        assert 'parameter_validation' in js_content, "parameter_validation not found in app.js"
        assert 'log_visualization' in js_content, "log_visualization not found in app.js"
        
        print("✓ Processing mode values are consistent across Python and JavaScript")
    
    def test_maintainability_scenarios(self):
        """Test various maintainability scenarios"""
        
        # Simulate the JavaScript logic
        def should_show_log_info(results):
            return bool(results.get('log_info') and results.get('processing_mode') == 'parameter_validation')
        
        # Test scenarios that demonstrate improved maintainability
        scenarios = [
            {
                'name': 'Existing parameter validation',
                'results': {
                    'processing_mode': 'parameter_validation',
                    'processor_used': 'pq_config_validator',
                    'log_info': {'total_frames': 100}
                },
                'expected': True
            },
            {
                'name': 'Existing grid comparison',
                'results': {
                    'processing_mode': 'log_visualization',
                    'processor_used': 'grid_comparison',
                    'log_info': {'total_frames': 100}
                },
                'expected': False
            },
            {
                'name': 'Existing firmware log visualizer',
                'results': {
                    'processing_mode': 'log_visualization',
                    'processor_used': 'firmware_log_visualizer',
                    'log_info': {'total_frames': 100}
                },
                'expected': False
            },
            {
                'name': 'Future new log processor (maintainability test)',
                'results': {
                    'processing_mode': 'log_visualization',
                    'processor_used': 'future_new_processor',
                    'log_info': {'total_frames': 100}
                },
                'expected': False
            },
            {
                'name': 'Another future processor (maintainability test)',
                'results': {
                    'processing_mode': 'log_visualization',
                    'processor_used': 'another_log_tool',
                    'log_info': {'total_frames': 100}
                },
                'expected': False
            }
        ]
        
        for scenario in scenarios:
            result = should_show_log_info(scenario['results'])
            expected = scenario['expected']
            assert result == expected, f"Failed for {scenario['name']}: got {result}, expected {expected}"
            print(f"✓ {scenario['name']}: {'Shows' if result else 'Hides'} log info correctly")
        
        print("✓ All maintainability scenarios pass - new processors work without code changes")
    
    def test_ui_test_file_updated(self):
        """Test that the UI test file has been properly updated"""
        
        test_file_path = Path(__file__).parent.parent / "data/ui/test_ui_behavior.html"
        assert test_file_path.exists(), "UI test file not found"
        
        with open(test_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check that it uses the new processing_mode approach
        assert 'processing_mode === \'parameter_validation\'' in content, "UI test doesn't use new logic"
        assert 'Parameter Validation Mode' in content, "UI test doesn't have parameter validation test"
        assert 'Log Visualization Mode' in content, "UI test doesn't have log visualization test"
        assert 'future-proof' in content, "UI test doesn't demonstrate future-proofing"
        
        print("✓ UI test file properly updated with new processing_mode logic")
    
    def test_comment_accuracy(self):
        """Test that comments accurately reflect the new logic"""
        
        js_file_path = Path(__file__).parent.parent.parent / "static/js/app.js"
        with open(js_file_path, 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        # Check for updated comment
        assert 'only for parameter validation mode' in js_content, "Comment not updated to reflect new logic"
        
        # Check that old comment is removed
        assert 'skip for Grid Comparison Plotter and Firmware Log Visualizer' not in js_content, "Old comment still present"
        
        print("✓ Comments accurately reflect the new processing_mode logic")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
