@echo off
REM PQ Config Validator Docker Deployment Script for Windows
REM Author: Ethan Li
REM Version: 1.0.0

setlocal enabledelayedexpansion

REM Configuration
set APP_NAME=PQ Config Validator
set IMAGE_NAME=pq-config-validator
set CONTAINER_NAME=pq-config-validator
set DEFAULT_PORT=8000

REM Colors (limited support in Windows)
set GREEN=[92m
set RED=[91m
set YELLOW=[93m
set BLUE=[94m
set NC=[0m

:main
echo %BLUE%================================%NC%
echo %BLUE%  %APP_NAME% Docker Deployment%NC%
echo %BLUE%  Author: Ethan Li%NC%
echo %BLUE%================================%NC%
echo.

REM Check command line argument
if "%1"=="" goto show_help
if "%1"=="help" goto show_help
if "%1"=="--help" goto show_help
if "%1"=="-h" goto show_help
if "%1"=="build" goto build
if "%1"=="start" goto start
if "%1"=="stop" goto stop
if "%1"=="restart" goto restart
if "%1"=="status" goto status
if "%1"=="logs" goto logs
if "%1"=="cleanup" goto cleanup

echo %RED%Unknown command: %1%NC%
echo.
goto show_help

:check_docker
echo %BLUE%Checking Docker installation...%NC%
docker --version >nul 2>&1
if errorlevel 1 (
    echo %RED%Docker is not installed or not in PATH%NC%
    echo Please install Docker Desktop for Windows
    exit /b 1
)

docker info >nul 2>&1
if errorlevel 1 (
    echo %RED%Docker is not running%NC%
    echo Please start Docker Desktop
    exit /b 1
)

echo %GREEN%Docker is installed and running%NC%
goto :eof

:check_docker_compose
echo %BLUE%Checking Docker Compose...%NC%
docker-compose --version >nul 2>&1
if not errorlevel 1 (
    set COMPOSE_CMD=docker-compose
    echo %GREEN%Docker Compose is available%NC%
    goto :eof
)

docker compose version >nul 2>&1
if not errorlevel 1 (
    set COMPOSE_CMD=docker compose
    echo %GREEN%Docker Compose is available%NC%
    goto :eof
)

echo %RED%Docker Compose is not available%NC%
echo Please install Docker Compose
exit /b 1

:create_directories
echo %BLUE%Creating necessary directories...%NC%
if not exist "data" mkdir data
if not exist "data\uploads" mkdir data\uploads
if not exist "data\sessions" mkdir data\sessions
if not exist "data\logs" mkdir data\logs
echo %GREEN%Directories created%NC%
goto :eof

:build
call :check_docker
call :check_docker_compose
call :create_directories

echo %BLUE%Building Docker image...%NC%
docker build -t %IMAGE_NAME%:latest .
if errorlevel 1 (
    echo %RED%Failed to build Docker image%NC%
    exit /b 1
)
echo %GREEN%Docker image built successfully%NC%
goto end

:start
call :check_docker
call :check_docker_compose
call :create_directories

echo %BLUE%Starting %APP_NAME%...%NC%
%COMPOSE_CMD% up -d
if errorlevel 1 (
    echo %RED%Failed to start application%NC%
    exit /b 1
)
echo %GREEN%Application started successfully%NC%
echo %BLUE%Application is running at: http://localhost:%DEFAULT_PORT%%NC%
echo %BLUE%Container name: %CONTAINER_NAME%%NC%
goto end

:stop
call :check_docker
call :check_docker_compose

echo %BLUE%Stopping %APP_NAME%...%NC%
%COMPOSE_CMD% down
if errorlevel 1 (
    echo %RED%Failed to stop application%NC%
    exit /b 1
)
echo %GREEN%Application stopped successfully%NC%
goto end

:restart
call :stop
timeout /t 2 /nobreak >nul
call :start
goto end

:status
call :check_docker

echo %BLUE%Application Status:%NC%
echo.

REM Check if container is running
docker ps --filter "name=%CONTAINER_NAME%" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | findstr %CONTAINER_NAME% >nul
if not errorlevel 1 (
    echo %GREEN%Container is running%NC%
    docker ps --filter "name=%CONTAINER_NAME%" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
) else (
    echo %YELLOW%Container is not running%NC%
)

echo.
echo %BLUE%Recent logs:%NC%
docker logs --tail 10 %CONTAINER_NAME% 2>nul
if errorlevel 1 echo %YELLOW%No logs available%NC%
goto end

:logs
call :check_docker

echo %BLUE%Showing logs for %APP_NAME%...%NC%
docker logs -f %CONTAINER_NAME%
goto end

:cleanup
call :check_docker
call :check_docker_compose

echo %YELLOW%This will remove all containers and images for %APP_NAME%%NC%
set /p confirm="Are you sure? (y/N): "
if /i not "%confirm%"=="y" (
    echo %BLUE%Cleanup cancelled%NC%
    goto end
)

echo %BLUE%Cleaning up...%NC%
%COMPOSE_CMD% down --rmi all --volumes --remove-orphans
docker image prune -f
echo %GREEN%Cleanup completed%NC%
goto end

:show_help
echo Usage: %0 [COMMAND]
echo.
echo Commands:
echo   build     Build the Docker image
echo   start     Start the application
echo   stop      Stop the application
echo   restart   Restart the application
echo   status    Show application status
echo   logs      Show application logs
echo   cleanup   Remove all containers and images
echo   help      Show this help message
echo.
echo Examples:
echo   %0 build     # Build the image
echo   %0 start     # Start the application
echo   %0 status    # Check if running
echo   %0 logs      # View logs
goto end

:end
echo.
echo %BLUE%Script completed%NC%
pause
