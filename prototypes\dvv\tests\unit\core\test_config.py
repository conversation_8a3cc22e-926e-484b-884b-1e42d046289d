#!/usr/bin/env python3
"""
Unit tests for config.py module

Tests configuration management functionality including:
- Configuration loading and validation
- Web application configuration
- Default settings and constants
- Environment-specific configurations
"""

import pytest
import sys
from pathlib import Path

# Add parent directories to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent))

from config import *


class TestConfiguration:
    """Test configuration management"""
    
    def test_web_applications_config(self):
        """Test that WEB_APPLICATIONS configuration is valid"""
        assert isinstance(WEB_APPLICATIONS, dict)
        assert len(WEB_APPLICATIONS) > 0
        
        # Test required fields for each application
        for app_id, app_config in WEB_APPLICATIONS.items():
            assert isinstance(app_id, str)
            assert isinstance(app_config, dict)
            
            # Required fields
            required_fields = ["name", "description", "icon", "url", "enabled", "author", "version"]
            for field in required_fields:
                assert field in app_config, f"Missing {field} in {app_id}"
                assert app_config[field] is not None, f"{field} is None in {app_id}"
    
    def test_logging_configuration(self):
        """Test logging configuration constants"""
        assert hasattr(sys.modules[__name__], 'LOG_LEVEL') or 'LOG_LEVEL' in globals()
        assert hasattr(sys.modules[__name__], 'LOGS_DIR') or 'LOGS_DIR' in globals()
    
    def test_upload_configuration(self):
        """Test upload-related configuration"""
        # Test that upload directory constants exist
        assert hasattr(sys.modules[__name__], 'UPLOAD_DIR') or 'UPLOAD_DIR' in globals()


if __name__ == "__main__":
    pytest.main([__file__])
