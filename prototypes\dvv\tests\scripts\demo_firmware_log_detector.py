#!/usr/bin/env python3
"""
Demo script for FirmwareLogDetector

This script demonstrates the FirmwareLogDetector functionality by testing
it with various types of log files and showing the detection results.

Author: <PERSON> Li
Date: 07-27-2025
"""

import tempfile
import sys
from pathlib import Path

# Add the project root to Python path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from log_processors import FirmwareLogDetector, TCFlashLogDetector

def test_detector_with_content(name: str, content: str):
    """Test the detector with given content and print results"""
    print(f"\n=== Testing {name} ===")
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write(content)
        f.flush()
        file_path = Path(f.name)
        
        try:
            # Test both detectors
            fw_result = FirmwareLogDetector.is_firmware_log(file_path)
            tc_result = TCFlashLogDetector.is_tc_flash_log(file_path)
            
            print(f"Firmware Log Detector: {fw_result}")
            print(f"TC Flash Log Detector: {tc_result}")
            
            if fw_result:
                # Get detailed info
                info = FirmwareLogDetector.get_log_info(file_path)
                print(f"Log Info:")
                for key, value in info.items():
                    print(f"  {key}: {value}")
            
            print(f"Content preview:")
            lines = content.strip().split('\n')[:5]
            for line in lines:
                print(f"  {line.strip()}")
            if len(content.strip().split('\n')) > 5:
                print("  ...")
                
        finally:
            try:
                file_path.unlink(missing_ok=True)
            except:
                pass

def main():
    """Run the demo with various test cases"""
    print("FirmwareLogDetector Demo")
    print("=" * 50)
    
    # Test case 1: Valid firmware log with Dolby debug format
    firmware_content_1 = """
L17-IC-filter_0.0416.txt firmware log content
Debug output from Dolby Vision processing

dBrightness(100)
dContrast(85)
dSaturation(90)
dBacklight(75)

gainPos_precisionRendering(50)
gainPos_dLocalContrast(60)
gainPos_dBrightness(45)

confidence(95)
precisionRenderingStrength(80)

mid_boost: 40
highlight_stretch: 35
shadow_drop: 20
"""
    test_detector_with_content("Valid Firmware Log (Dolby Debug Format)", firmware_content_1)
    
    # Test case 2: Valid firmware log with level parameters
    firmware_content_2 = """
Firmware processing log
Level-based parameter adjustments

level_brightness: 110
level_contrast: 88
level_saturation: 92
level_backlight: 78

processing_mode: enhanced
quality_setting: high

gain_adjustment: 1.2
offset_correction: -0.5
"""
    test_detector_with_content("Valid Firmware Log (Level Parameters)", firmware_content_2)
    
    # Test case 3: TC Flash log (should be detected as firmware log too)
    tc_flash_content = """
fallback: 0
Config values:
intensity_level: 100
gain_red: 50
gain_green: 60
gain_blue: 70

DM values:
LocalMappingStrength 80
UpMappingStrength 90

fallback: 0
Config values:
intensity_level: 105
gain_red: 55
gain_green: 65
gain_blue: 75
"""
    test_detector_with_content("TC Flash Log", tc_flash_content)
    
    # Test case 4: Invalid log file
    invalid_content = """
This is just a regular text file
with no firmware parameters
or structured data
"""
    test_detector_with_content("Invalid Log File", invalid_content)
    
    # Test case 5: Structured firmware log
    structured_content = """
[Firmware Log Start]
Processing parameters:

brightness_adjustment: 105
contrast_enhancement: 90
saturation_boost: 95
backlight_control: 80

[Advanced Settings]
precision_rendering: enabled
local_contrast: 65
dynamic_range: extended

[Processing Results]
confidence_score: 92
quality_metric: 8.5
"""
    test_detector_with_content("Structured Firmware Log", structured_content)
    
    # Test case 6: Mixed format log
    mixed_content = """
Firmware processing started
Initial parameters loaded

dBrightness(120)
dContrast(95)

mid_boost: 45
detail_boost: 25
"""
    test_detector_with_content("Mixed Format Log", mixed_content)
    
    print(f"\n{'=' * 50}")
    print("Demo completed!")
    print("\nSummary:")
    print("- FirmwareLogDetector identifies files that can be processed by firmware_log_visualizer")
    print("- It detects various parameter patterns: Dolby debug format, level parameters, structured format")
    print("- TC Flash logs are also detected as firmware logs (since firmware_log_visualizer can process them)")
    print("- Files without sufficient parameter patterns are rejected")
    print("- The detector provides detailed metadata about detected parameters")

if __name__ == "__main__":
    main()
