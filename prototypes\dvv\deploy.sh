#!/bin/bash
# Deployment script for Linux/macOS LAN deployment

echo "Starting DVV"

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed"
    echo "Please install Python 3.8 or later"
    exit 1
fi

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Install dependencies
echo "Installing dependencies..."
pip install -r requirements.txt

# Set environment variables for LAN deployment
export HOST=0.0.0.0
export PORT=8000
export LOG_LEVEL=INFO
export DEBUG=false

# Get local IP address
if command -v ip &> /dev/null; then
    LOCAL_IP=$(ip route get 1 | awk '{print $7; exit}')
elif command -v ifconfig &> /dev/null; then
    LOCAL_IP=$(ifconfig | grep -Eo 'inet (addr:)?([0-9]*\.){3}[0-9]*' | grep -Eo '([0-9]*\.){3}[0-9]*' | grep -v '127.0.0.1' | head -1)
else
    LOCAL_IP="<your-ip-address>"
fi

echo ""
echo "========================================"
echo "DVV"
echo "========================================"
echo "Server will be accessible at:"
echo "  Local:    http://localhost:8000"
echo "  Network:  http://$LOCAL_IP:8000"
echo "========================================"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

# Start the application
python3 run.py --host $HOST --port $PORT --log-level $LOG_LEVEL
