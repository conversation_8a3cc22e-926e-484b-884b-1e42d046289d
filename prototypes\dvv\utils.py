"""
Utility functions for the Parameter Detection Tool
"""

import os
import re
import logging
from pathlib import Path
from typing import List, Set, Optional
from config import ENCODING_FALLBACKS, MAX_LINE_LENGTH, IGNORE_COMMENT_PATTERNS
from exceptions import FileProcessingError, ValidationError

def setup_logging(log_level: str = "INFO", log_dir: Path = None):
    """Setup logging configuration"""
    if log_dir:
        log_dir.mkdir(exist_ok=True)
        log_file = log_dir / "app.log"
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
    else:
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )

def sanitize_filename(filename: str) -> str:
    """Sanitize filename to prevent directory traversal and other security issues"""
    # Remove path separators and other dangerous characters
    sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # Remove leading/trailing dots and spaces
    sanitized = sanitized.strip('. ')
    # Limit length
    if len(sanitized) > 255:
        name, ext = os.path.splitext(sanitized)
        sanitized = name[:255-len(ext)] + ext
    return sanitized or "unnamed_file"

def validate_file_content(content: bytes, max_size: int) -> str:
    """Validate and decode file content"""
    if len(content) > max_size:
        raise ValidationError(f"File size {len(content)} exceeds maximum allowed size {max_size}")
    
    # Try different encodings
    for encoding in ENCODING_FALLBACKS:
        try:
            return content.decode(encoding)
        except UnicodeDecodeError:
            continue
    
    raise FileProcessingError("Unable to decode file content with any supported encoding")

def is_comment_line(line: str) -> bool:
    """Check if a line is a comment based on common comment patterns"""
    line = line.strip()
    if not line:
        return True
    
    for pattern in IGNORE_COMMENT_PATTERNS:
        if re.match(pattern, line):
            return True
    return False

def validate_line_length(line: str, line_num: int) -> str:
    """Validate and truncate line if too long"""
    if len(line) > MAX_LINE_LENGTH:
        logging.warning(f"Line {line_num} truncated from {len(line)} to {MAX_LINE_LENGTH} characters")
        return line[:MAX_LINE_LENGTH] + "... [TRUNCATED]"
    return line

def safe_file_read(file_path: Path, max_size: int) -> str:
    """Safely read file content with proper error handling"""
    try:
        if not file_path.exists():
            raise FileProcessingError(f"File does not exist: {file_path}")
        
        if not file_path.is_file():
            raise FileProcessingError(f"Path is not a file: {file_path}")
        
        file_size = file_path.stat().st_size
        if file_size > max_size:
            raise ValidationError(f"File size {file_size} exceeds maximum allowed size {max_size}")
        
        with open(file_path, 'rb') as f:
            content = f.read()
        
        return validate_file_content(content, max_size)
        
    except (OSError, IOError) as e:
        raise FileProcessingError(f"Error reading file {file_path}: {str(e)}")

def create_safe_directory(path: Path, parents: bool = True) -> Path:
    """Safely create directory with proper error handling"""
    try:
        path.mkdir(parents=parents, exist_ok=True)
        return path
    except (OSError, IOError) as e:
        raise FileProcessingError(f"Error creating directory {path}: {str(e)}")

def validate_upload_directory(upload_dir: Path) -> Path:
    """
    Validate and setup upload directory with comprehensive error handling

    Args:
        upload_dir: Path to the upload directory

    Returns:
        Path: Validated and created upload directory path

    Raises:
        FileProcessingError: If directory cannot be created or is not writable
    """
    try:
        # Convert to absolute path
        upload_dir = upload_dir.resolve()

        # Check if parent directory exists and is writable
        parent_dir = upload_dir.parent
        if not parent_dir.exists():
            raise FileProcessingError(f"Parent directory does not exist: {parent_dir}")

        if not os.access(parent_dir, os.W_OK):
            raise FileProcessingError(f"Parent directory is not writable: {parent_dir}")

        # Create the upload directory if it doesn't exist
        upload_dir.mkdir(parents=True, exist_ok=True)

        # Verify the directory is writable
        if not os.access(upload_dir, os.W_OK):
            raise FileProcessingError(f"Upload directory is not writable: {upload_dir}")

        # Test write access by creating a temporary file
        test_file = upload_dir / ".write_test"
        try:
            test_file.touch()
            test_file.unlink()
        except (OSError, IOError) as e:
            raise FileProcessingError(f"Cannot write to upload directory {upload_dir}: {str(e)}")

        return upload_dir

    except (OSError, IOError) as e:
        raise FileProcessingError(f"Error validating upload directory {upload_dir}: {str(e)}")

def validate_user_id_format(user_id: str) -> bool:
    """Validate user ID format with comprehensive checks"""
    if not user_id or not isinstance(user_id, str):
        return False
    
    user_id = user_id.strip()
    
    # Check length
    if len(user_id) < 1 or len(user_id) > 50:
        return False
    
    # Check for valid characters (alphanumeric, spaces, hyphens, underscores)
    if not re.match(r'^[a-zA-Z0-9\s\-_]+$', user_id):
        return False
    
    # Check for reserved names
    reserved_names = {'admin', 'root', 'system', 'api', 'www', 'ftp', 'mail'}
    if user_id.lower() in reserved_names:
        return False
    
    return True

def get_file_extension(filename: str) -> str:
    """Get file extension in lowercase"""
    return Path(filename).suffix.lower()

def format_file_size(size_bytes: int) -> str:
    """Format file size in human-readable format"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"
