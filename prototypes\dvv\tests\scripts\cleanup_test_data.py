#!/usr/bin/env python3
"""
Test Data Cleanup Script

This script cleans up test artifacts, temporary files, and cache directories
created during testing to maintain a clean test environment.

Usage:
    python tests/scripts/cleanup_test_data.py [--dry-run] [--verbose]
"""

import os
import shutil
import argparse
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def cleanup_pycache(root_dir: Path, dry_run: bool = False, verbose: bool = False):
    """Remove all __pycache__ directories recursively."""
    pycache_dirs = list(root_dir.rglob("__pycache__"))
    
    if verbose:
        logger.info(f"Found {len(pycache_dirs)} __pycache__ directories")
    
    for pycache_dir in pycache_dirs:
        if verbose:
            logger.info(f"Removing: {pycache_dir}")
        
        if not dry_run:
            try:
                shutil.rmtree(pycache_dir)
                logger.info(f"✓ Removed: {pycache_dir}")
            except Exception as e:
                logger.error(f"✗ Failed to remove {pycache_dir}: {e}")

def cleanup_pytest_cache(root_dir: Path, dry_run: bool = False, verbose: bool = False):
    """Remove pytest cache directories."""
    pytest_cache_dirs = list(root_dir.rglob(".pytest_cache"))
    
    if verbose:
        logger.info(f"Found {len(pytest_cache_dirs)} .pytest_cache directories")
    
    for cache_dir in pytest_cache_dirs:
        if verbose:
            logger.info(f"Removing: {cache_dir}")
        
        if not dry_run:
            try:
                shutil.rmtree(cache_dir)
                logger.info(f"✓ Removed: {cache_dir}")
            except Exception as e:
                logger.error(f"✗ Failed to remove {cache_dir}: {e}")

def cleanup_test_uploads(root_dir: Path, dry_run: bool = False, verbose: bool = False):
    """Clean up test upload directories."""
    uploads_dir = root_dir / "uploads"
    
    if not uploads_dir.exists():
        if verbose:
            logger.info("No uploads directory found")
        return
    
    test_user_dirs = []
    for item in uploads_dir.iterdir():
        if item.is_dir() and ("test_" in item.name.lower() or "session_" in item.name.lower()):
            test_user_dirs.append(item)
    
    if verbose:
        logger.info(f"Found {len(test_user_dirs)} test user directories")
    
    for test_dir in test_user_dirs:
        if verbose:
            logger.info(f"Removing test directory: {test_dir}")
        
        if not dry_run:
            try:
                shutil.rmtree(test_dir)
                logger.info(f"✓ Removed: {test_dir}")
            except Exception as e:
                logger.error(f"✗ Failed to remove {test_dir}: {e}")

def cleanup_test_artifacts(root_dir: Path, dry_run: bool = False, verbose: bool = False):
    """Clean up test artifacts like generated plots and reports."""
    artifacts_dir = root_dir / "tests" / "artifacts"
    
    if not artifacts_dir.exists():
        if verbose:
            logger.info("No test artifacts directory found")
        return
    
    # Clean plots
    plots_dir = artifacts_dir / "plots"
    if plots_dir.exists():
        plot_files = list(plots_dir.glob("*.png")) + list(plots_dir.glob("*.jpg"))
        if verbose:
            logger.info(f"Found {len(plot_files)} plot files to clean")
        
        for plot_file in plot_files:
            if verbose:
                logger.info(f"Removing plot: {plot_file}")
            
            if not dry_run:
                try:
                    plot_file.unlink()
                    logger.info(f"✓ Removed: {plot_file}")
                except Exception as e:
                    logger.error(f"✗ Failed to remove {plot_file}: {e}")
    
    # Clean reports
    reports_dir = artifacts_dir / "reports"
    if reports_dir.exists():
        report_files = list(reports_dir.glob("*.html")) + list(reports_dir.glob("*.json"))
        if verbose:
            logger.info(f"Found {len(report_files)} report files to clean")
        
        for report_file in report_files:
            if verbose:
                logger.info(f"Removing report: {report_file}")
            
            if not dry_run:
                try:
                    report_file.unlink()
                    logger.info(f"✓ Removed: {report_file}")
                except Exception as e:
                    logger.error(f"✗ Failed to remove {report_file}: {e}")

def cleanup_temp_files(root_dir: Path, dry_run: bool = False, verbose: bool = False):
    """Clean up temporary files created during testing."""
    temp_patterns = ["*.tmp", "*.temp", "temp_*", "test_*.txt"]
    
    temp_files = []
    for pattern in temp_patterns:
        temp_files.extend(root_dir.rglob(pattern))
    
    if verbose:
        logger.info(f"Found {len(temp_files)} temporary files")
    
    for temp_file in temp_files:
        if temp_file.is_file() and "test" in temp_file.name.lower():
            if verbose:
                logger.info(f"Removing temp file: {temp_file}")
            
            if not dry_run:
                try:
                    temp_file.unlink()
                    logger.info(f"✓ Removed: {temp_file}")
                except Exception as e:
                    logger.error(f"✗ Failed to remove {temp_file}: {e}")

def main():
    parser = argparse.ArgumentParser(description="Clean up test data and artifacts")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be deleted without actually deleting")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--all", action="store_true", help="Clean all types of test data")
    parser.add_argument("--cache", action="store_true", help="Clean only cache directories")
    parser.add_argument("--uploads", action="store_true", help="Clean only test upload directories")
    parser.add_argument("--artifacts", action="store_true", help="Clean only test artifacts")
    parser.add_argument("--temp", action="store_true", help="Clean only temporary files")
    
    args = parser.parse_args()
    
    # Get the project root directory
    script_dir = Path(__file__).parent
    root_dir = script_dir.parent.parent
    
    logger.info(f"Cleaning test data in: {root_dir}")
    
    if args.dry_run:
        logger.info("DRY RUN MODE - No files will actually be deleted")
    
    # Determine what to clean
    clean_all = args.all or not any([args.cache, args.uploads, args.artifacts, args.temp])
    
    if clean_all or args.cache:
        logger.info("Cleaning cache directories...")
        cleanup_pycache(root_dir, args.dry_run, args.verbose)
        cleanup_pytest_cache(root_dir, args.dry_run, args.verbose)
    
    if clean_all or args.uploads:
        logger.info("Cleaning test upload directories...")
        cleanup_test_uploads(root_dir, args.dry_run, args.verbose)
    
    if clean_all or args.artifacts:
        logger.info("Cleaning test artifacts...")
        cleanup_test_artifacts(root_dir, args.dry_run, args.verbose)
    
    if clean_all or args.temp:
        logger.info("Cleaning temporary files...")
        cleanup_temp_files(root_dir, args.dry_run, args.verbose)
    
    logger.info("Cleanup completed!")

if __name__ == "__main__":
    main()
