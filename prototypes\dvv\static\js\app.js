// Main JavaScript file for the Parameter Detection Tool

// Utility functions
function showAlert(message, type, containerId = 'alertContainer') {
    const alertContainer = document.getElementById(containerId);
    if (!alertContainer) return;
    
    alertContainer.innerHTML = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
}

function showLoading(show = true) {
    const spinner = document.querySelector('.processing-spinner');
    const uploadForm = document.getElementById('uploadForm');
    
    if (spinner) {
        if (show) {
            spinner.classList.add('show');
            if (uploadForm) uploadForm.style.display = 'none';
        } else {
            spinner.classList.remove('show');
            if (uploadForm) uploadForm.style.display = 'block';
        }
    }
}

function showResults(show = true) {
    const resultsContainer = document.querySelector('.results-container');
    if (resultsContainer) {
        if (show) {
            resultsContainer.classList.add('show');
        } else {
            resultsContainer.classList.remove('show');
        }
    }
}

// File upload drag and drop functionality
function initializeFileUpload() {
    const fileUploadArea = document.querySelector('.file-upload-area');
    const fileInput = document.getElementById('fileInput');
    
    if (!fileUploadArea || !fileInput) return;
    
    // Click to upload
    fileUploadArea.addEventListener('click', () => {
        fileInput.click();
    });
    
    // Drag and drop events
    fileUploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        fileUploadArea.classList.add('dragover');
    });
    
    fileUploadArea.addEventListener('dragleave', (e) => {
        e.preventDefault();
        fileUploadArea.classList.remove('dragover');
    });
    
    fileUploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        fileUploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            updateFileInfo(files[0]);
        }
    });
    
    // File input change
    fileInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            updateFileInfo(e.target.files[0]);
        }
    });
}

function updateFileInfo(file) {
    const fileInfo = document.querySelector('.file-info');
    if (!fileInfo) return;
    
    const fileSize = (file.size / 1024).toFixed(2);
    fileInfo.innerHTML = `
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <strong><i class="fas fa-file-text me-2"></i>${file.name}</strong>
                <div class="text-muted small">Size: ${fileSize} KB</div>
            </div>
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearFile()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    fileInfo.style.display = 'block';
}

function clearFile() {
    const fileInput = document.getElementById('fileInput');
    const fileInfo = document.querySelector('.file-info');
    
    if (fileInput) fileInput.value = '';
    if (fileInfo) fileInfo.style.display = 'none';
}

// Format file processing results
function formatResults(results) {
    let html = '';

    // Check processing mode
    const isLogVisualization = results.processing_mode === 'log_visualization';

    // For log visualization mode
    if (isLogVisualization) {
        return formatLogVisualizationResults(results);
    }

    // Traditional parameter validation mode
    const hasUndefinedParams = results.undefined_parameters && results.undefined_parameters.length > 0;
    const hasWarnings = results.validation_warnings && results.validation_warnings.length > 0;

    if (!hasUndefinedParams && !hasWarnings) {
        return `
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <strong>Excellent!</strong> No issues found in your file.
                ${results.external_tool_used ? '<br><small>External validation tool was used for comprehensive analysis.</small>' : ''}
            </div>
        `;
    }

    // Summary alert
    if (hasUndefinedParams && hasWarnings) {
        html += `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Found ${results.undefined_parameters.length} undefined parameter(s) and ${results.validation_warnings.length} validation warning(s)</strong>
                ${results.external_tool_used ? '<br><small>Analysis includes external Dolby Vision validation tool results.</small>' : ''}
            </div>
        `;
    } else if (hasUndefinedParams) {
        html += `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Found ${results.undefined_parameters.length} undefined parameter(s)</strong>
            </div>
        `;
    } else if (hasWarnings) {
        html += `
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>Found ${results.validation_warnings.length} validation warning(s)</strong>
                <br><small>External Dolby Vision validation tool results.</small>
            </div>
        `;
    }

    // Undefined parameters section
    if (hasUndefinedParams) {
        html += `
            <div class="mt-4">
                <h5><i class="fas fa-search me-2"></i>Undefined Parameters</h5>
                <p class="text-muted">Parameters referenced but not defined in the file:</p>
        `;

        results.undefined_parameters.forEach((param, index) => {
            html += `
                <div class="parameter-result">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <div>
                            <span class="parameter-name">${param.parameter_name}</span>
                        </div>
                        <span class="line-number">Line ${param.line_number}</span>
                    </div>
                    <div class="line-content">${param.line_content}</div>
                </div>
            `;
        });

        html += '</div>';
    }

    // Validation warnings section
    if (hasWarnings) {
        html += `
            <div class="mt-4">
                <h5><i class="fas fa-exclamation-triangle me-2"></i>Validation Warnings</h5>
                <p class="text-muted">Issues detected by external validation tool:</p>
        `;

        results.validation_warnings.forEach((warning, index) => {
            const warningClass = warning.warning_type === 'validation_error' ? 'warning-error' : 'warning-info';
            const icon = warning.warning_type === 'validation_error' ? 'fas fa-times-circle' : 'fas fa-exclamation-circle';

            html += `
                <div class="validation-warning ${warningClass}">
                    <div class="d-flex align-items-start mb-2">
                        <i class="${icon} me-2 mt-1"></i>
                        <div class="flex-grow-1">
                            <div class="warning-message">${warning.message}</div>
            `;

            if (warning.parameter) {
                html += `<div class="warning-details mt-2">
                    <strong>Parameter:</strong> <code>${warning.parameter}</code>`;

                if (warning.value) {
                    html += ` <strong>Value:</strong> <code>${warning.value}</code>`;
                }

                if (warning.suggested_range) {
                    html += ` <strong>Suggested Range:</strong> <code>${warning.suggested_range}</code>`;
                }

                html += '</div>';
            }

            html += `
                        </div>
                    </div>
                </div>
            `;
        });

        html += '</div>';
    }

    return html;
}

// Format log visualization results
function formatLogVisualizationResults(results) {
    let html = '';

    // Header with processing information
    if (results.success) {
        html += `
            <div class="alert alert-success">
                <i class="fas fa-chart-line me-2"></i>
                <strong>Log Processing Complete!</strong> ${results.message}
                <br><small>Processor used: ${results.processor_used || 'auto-detected'}</small>
            </div>
        `;
    } else {
        html += `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Processing Failed:</strong> ${results.message}
                ${results.processor_used ? `<br><small>Attempted processor: ${results.processor_used}</small>` : ''}
            </div>
        `;
        return html;
    }

    // Display log file information (only for parameter validation mode)
    if (results.log_info && results.processing_mode === 'parameter_validation') {
        html += `
            <div class="card mb-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>Log File Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
        `;

        const info = results.log_info.additional_info || {};
        if (info.total_frames) {
            html += `
                        <div class="col-md-3">
                            <strong>Total Frames:</strong><br>
                            <span class="badge bg-primary">${info.total_frames}</span>
                        </div>
            `;
        }
        if (info.config_parameters !== undefined) {
            html += `
                        <div class="col-md-3">
                            <strong>Config Parameters:</strong><br>
                            <span class="badge bg-info">${info.config_parameters}</span>
                        </div>
            `;
        }
        if (info.dm_parameters !== undefined) {
            html += `
                        <div class="col-md-3">
                            <strong>DM Parameters:</strong><br>
                            <span class="badge bg-info">${info.dm_parameters}</span>
                        </div>
            `;
        }
        if (info.total_parameters) {
            html += `
                        <div class="col-md-3">
                            <strong>Total Parameters:</strong><br>
                            <span class="badge bg-success">${info.total_parameters}</span>
                        </div>
            `;
        }

        html += `
                    </div>
                </div>
            </div>
        `;
    }

    // Display generated visualizations using static_files if available, fallback to output_files
    const visualizationFiles = results.static_files && results.static_files.length > 0
        ? results.static_files
        : (results.output_files && results.output_files.length > 0
            ? results.output_files.map(filename => ({
                filename: filename,
                unique_filename: filename,
                url: `/plots/${filename}`
              }))
            : []);

    if (visualizationFiles.length > 0) {
        html += `
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-images me-2"></i>Generated Visualizations
                    </h5>
                </div>
                <div class="card-body">
        `;

        // Add application-specific header based on processor type
        if (results.processor_used === 'grid_comparison') {
            html += `
                    <div class="alert alert-info mb-3">
                        <i class="fas fa-th me-2"></i>
                        <strong>IDK-IC In-and-Out:</strong> Comprehensive parameter comparison in grid layout format.
                    </div>
            `;
        } else if (results.processor_used === 'log_parser') {
            html += `
                    <div class="alert alert-info mb-3">
                        <i class="fas fa-chart-line me-2"></i>
                        <strong>MTK FW Log Visualizer:</strong> Individual parameter plots and overview charts.
                    </div>
            `;
        }

        visualizationFiles.forEach((fileInfo, index) => {
            const imageUrl = fileInfo.url;
            const originalFilename = fileInfo.filename;
            const downloadFilename = fileInfo.unique_filename || originalFilename;
            const imageName = originalFilename.replace(/^.*_/, '').replace('.png', '');

            html += `
                    <div class="mb-4">
                        <h6 class="text-muted">${imageName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h6>
                        <div class="text-center">
                            <img src="${imageUrl}"
                                 class="img-fluid border rounded shadow-sm"
                                 alt="${imageName}"
                                 style="max-width: 100%; height: auto; cursor: pointer;"
                                 onclick="openImageModal('${imageUrl}', '${imageName}')">
                        </div>
                        <div class="mt-2 text-center">
                            <a href="${imageUrl}" download="${downloadFilename}" class="unified-download-btn btn-sm">
                                <i class="fas fa-download download-icon"></i>Download
                            </a>
                            <button class="btn btn-sm btn-outline-secondary ms-2" onclick="openImageModal('${imageUrl}', '${imageName}')">
                                <i class="fas fa-expand me-1"></i>View Full Size
                            </button>
                        </div>
                    </div>
            `;

            if (index < visualizationFiles.length - 1) {
                html += '<hr>';
            }
        });

        html += `
                </div>
            </div>
        `;
    }

    return html;
}

// Open image in modal for full-size viewing
function openImageModal(imageUrl, imageName) {
    // Create modal if it doesn't exist
    let modal = document.getElementById('imageModal');
    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'imageModal';
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="imageModalTitle">Image Viewer</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body text-center">
                        <img id="modalImage" src="" class="img-fluid" alt="">
                    </div>
                    <div class="modal-footer">
                        <a id="modalDownloadBtn" href="" download="" class="unified-download-btn">
                            <i class="fas fa-download download-icon"></i>Download
                        </a>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    // Update modal content
    document.getElementById('imageModalTitle').textContent = imageName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    document.getElementById('modalImage').src = imageUrl;
    document.getElementById('modalDownloadBtn').href = imageUrl;
    document.getElementById('modalDownloadBtn').download = imageUrl.split('/').pop();

    // Show modal
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeFileUpload();

    // Debug: Verify download function is loaded
    console.log('=== APP.JS LOADED ===');
    console.log('downloadResults function available:', typeof downloadResults);
    console.log('window.downloadResults available:', typeof window.downloadResults);
});

function downloadResults(format = 'html') {
    console.log('=== DOWNLOAD FUNCTION CALLED ===');
    console.log('Format requested:', format);
    console.log('Session ID:', window.sessionId);
    console.log('Function source:', downloadResults.toString().substring(0, 100) + '...');

    // Check if session exists
    if (!window.sessionId) {
        console.error('No session ID found');
        showAlert('Please upload and analyze a file first', 'warning');
        return;
    }

    // Validate format
    const validFormats = ['html'];
    if (!validFormats.includes(format)) {
        showAlert('Unsupported download format', 'danger');
        return;
    }

    try {
        // Show loading message
        const formatNames = {
            'html': 'HTML Report'
        };

        showAlert(`Generating ${formatNames[format]}...`, 'info');

        // Create download URL
        const downloadUrl = `/api/download-results/${window.sessionId}?format=${format}`;
        console.log('Download URL:', downloadUrl);

        // Use fetch to handle the download with better error handling
        fetch(downloadUrl)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.blob();
            })
            .then(blob => {
                // Create download link
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;

                // Get filename from response headers or generate one
                const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
                const extension = format === 'markdown' ? 'md' : format;
                link.download = `pq_config_analysis_${timestamp}.${extension}`;

                // Trigger download
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Clean up
                window.URL.revokeObjectURL(url);

                // Show success message
                showAlert(`${formatNames[format]} downloaded successfully`, 'success');
            })
            .catch(error => {
                console.error('Download error:', error);
                showAlert(`Download failed: ${error.message}`, 'danger');
            });

    } catch (error) {
        console.error('Download function error:', error);
        showAlert('Download failed, please try again', 'danger');
    }
}

/**
 * Create a unified download button component
 * @param {Object} options - Configuration options for the download button
 * @param {string} options.text - Button text (default: "Download")
 * @param {string} options.icon - Font Awesome icon class (default: "fas fa-download")
 * @param {string} options.size - Button size: "sm", "md", "lg" (default: "md")
 * @param {Function} options.onClick - Click handler function
 * @param {string} options.href - Direct download URL (for file downloads)
 * @param {string} options.downloadFilename - Filename for direct downloads
 * @returns {HTMLElement} - The created button element
 */
function createUnifiedDownloadButton(options = {}) {
    const {
        text = "Download",
        icon = "fas fa-download",
        size = "md",
        onClick = null,
        href = null,
        downloadFilename = null
    } = options;

    // Create button element
    let button;
    if (href) {
        button = document.createElement('a');
        button.href = href;
        if (downloadFilename) {
            button.download = downloadFilename;
        }
        button.target = '_blank';
    } else {
        button = document.createElement('button');
        button.type = 'button';
    }

    // Set base classes
    button.className = `unified-download-btn${size === 'sm' ? ' btn-sm' : size === 'lg' ? ' btn-lg' : ''}`;

    // Set inner HTML with icon and text
    button.innerHTML = `
        <i class="${icon} download-icon"></i>
        ${text}
    `;

    // Add click handler
    if (onClick) {
        button.addEventListener('click', onClick);
    }

    return button;
}

// Export functions for global use
window.showAlert = showAlert;
window.showLoading = showLoading;
window.showResults = showResults;
window.clearFile = clearFile;
window.formatResults = formatResults;
window.downloadResults = downloadResults;
window.createUnifiedDownloadButton = createUnifiedDownloadButton;
