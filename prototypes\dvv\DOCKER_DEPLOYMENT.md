# PQ Config Validator - Docker Deployment Guide

**Author:** <PERSON>  
**Version:** 1.0.0  
**Application:** PQ Config Validator

## Overview

This guide provides comprehensive instructions for deploying the PQ Config Validator application using Docker. The application supports both .txt and .cfg file formats for PQ configuration analysis with external Dolby Vision tool validation.

## Prerequisites

### System Requirements
- **Docker:** Version 20.10 or higher
- **Docker Compose:** Version 2.0 or higher (or docker-compose v1.29+)
- **Operating System:** Linux, macOS, or Windows with Docker Desktop
- **Memory:** Minimum 512MB RAM for the container
- **Storage:** At least 1GB free space for images and data

### Network Requirements
- **Port 8000:** Default application port (configurable)
- **LAN Access:** For network deployment, ensure firewall allows inbound connections

## Quick Start

### 1. Clone and Navigate
```bash
cd prototypes/detect_undef_pq_para
```

### 2. Build and Start (Linux/macOS)
```bash
# Make deployment script executable
chmod +x docker-deploy.sh

# Build the Docker image
./docker-deploy.sh build

# Start the application
./docker-deploy.sh start
```

### 3. Build and Start (Windows)
```cmd
# Build the Docker image
docker-deploy.bat build

# Start the application
docker-deploy.bat start
```

### 4. Access Application
Open your browser and navigate to: `http://localhost:8000`

## Detailed Deployment Steps

### Step 1: Prepare Environment

1. **Create data directories:**
```bash
mkdir -p data/uploads data/sessions data/logs
```

2. **Configure environment (optional):**
```bash
cp .env.production .env
# Edit .env file with your specific settings
```

### Step 2: Build Docker Image

**Using deployment script:**
```bash
./docker-deploy.sh build
```

**Manual build:**
```bash
docker build -t pq-config-validator:latest .
```

### Step 3: Start Application

**Using Docker Compose:**
```bash
docker-compose up -d
```

**Using deployment script:**
```bash
./docker-deploy.sh start
```

### Step 4: Verify Deployment

**Check container status:**
```bash
./docker-deploy.sh status
```

**View logs:**
```bash
./docker-deploy.sh logs
```

**Health check:**
```bash
curl -f http://localhost:8000/
```

## Configuration Options

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `HOST` | `0.0.0.0` | Bind address |
| `PORT` | `8000` | Application port |
| `DEBUG` | `false` | Debug mode |
| `LOG_LEVEL` | `INFO` | Logging level |
| `SESSION_TIMEOUT_HOURS` | `24` | Session timeout |
| `MAX_FILE_SIZE` | `10485760` | Max upload size (bytes) |

### Port Configuration

**Default port mapping:**
```yaml
ports:
  - "8000:8000"
```

**Alternative port mappings:**
```yaml
ports:
  - "8080:8000"  # External port 8080
  - "80:8000"    # HTTP port (requires root/admin)
```

### Volume Mounts

**Persistent data:**
```yaml
volumes:
  - ./data/uploads:/app/uploads    # Uploaded files
  - ./data/sessions:/app/sessions  # User sessions
  - ./data/logs:/app/logs          # Application logs
```

## Network Access Configuration

### LAN Access Setup

1. **Update docker-compose.yml:**
```yaml
services:
  pq-config-validator:
    ports:
      - "0.0.0.0:8000:8000"  # Bind to all interfaces
```

2. **Configure firewall (Linux):**
```bash
sudo ufw allow 8000/tcp
```

3. **Configure Windows Firewall:**
- Open Windows Defender Firewall
- Add inbound rule for port 8000
- Allow TCP connections

### Access from Network

**Find your IP address:**
```bash
# Linux/macOS
ip addr show | grep inet
# or
ifconfig | grep inet

# Windows
ipconfig
```

**Access URL format:**
```
http://YOUR_IP_ADDRESS:8000
```

## Management Commands

### Using Deployment Scripts

**Linux/macOS:**
```bash
./docker-deploy.sh build     # Build image
./docker-deploy.sh start     # Start application
./docker-deploy.sh stop      # Stop application
./docker-deploy.sh restart   # Restart application
./docker-deploy.sh status    # Show status
./docker-deploy.sh logs      # View logs
./docker-deploy.sh cleanup   # Remove all containers/images
```

**Windows:**
```cmd
docker-deploy.bat build     # Build image
docker-deploy.bat start     # Start application
docker-deploy.bat stop      # Stop application
docker-deploy.bat restart   # Restart application
docker-deploy.bat status    # Show status
docker-deploy.bat logs      # View logs
docker-deploy.bat cleanup   # Remove all containers/images
```

### Manual Docker Commands

**Start container:**
```bash
docker-compose up -d
```

**Stop container:**
```bash
docker-compose down
```

**View logs:**
```bash
docker logs -f pq-config-validator
```

**Execute commands in container:**
```bash
docker exec -it pq-config-validator bash
```

## Data Persistence

### Backup Strategy

**Backup uploaded files:**
```bash
tar -czf backup-uploads-$(date +%Y%m%d).tar.gz data/uploads/
```

**Backup sessions:**
```bash
tar -czf backup-sessions-$(date +%Y%m%d).tar.gz data/sessions/
```

### Restore Data

**Restore uploads:**
```bash
tar -xzf backup-uploads-YYYYMMDD.tar.gz
```

## Security Considerations

### Production Security

1. **Use non-root user:** ✅ Implemented in Dockerfile
2. **Limit resource usage:** ✅ Configured in docker-compose.yml
3. **Network isolation:** ✅ Custom Docker network
4. **File permissions:** ✅ Proper ownership in container

### Additional Security Measures

1. **Enable HTTPS:** Use reverse proxy (nginx/Apache)
2. **Firewall rules:** Restrict access to specific IP ranges
3. **Regular updates:** Keep Docker and base images updated
4. **Monitor logs:** Set up log monitoring and alerting

## Troubleshooting

### Common Issues

**Port already in use:**
```bash
# Check what's using the port
netstat -tulpn | grep 8000
# or
lsof -i :8000

# Change port in docker-compose.yml
ports:
  - "8001:8000"
```

**Permission denied:**
```bash
# Fix file permissions
sudo chown -R $USER:$USER data/
chmod -R 755 data/
```

**Container won't start:**
```bash
# Check logs
docker logs pq-config-validator

# Check Docker daemon
sudo systemctl status docker
```

### Health Checks

**Container health:**
```bash
docker inspect pq-config-validator | grep Health -A 10
```

**Application health:**
```bash
curl -f http://localhost:8000/
```

## Performance Optimization

### Resource Limits

**Memory optimization:**
```yaml
deploy:
  resources:
    limits:
      memory: 512M
    reservations:
      memory: 128M
```

**CPU optimization:**
```yaml
deploy:
  resources:
    limits:
      cpus: '1.0'
    reservations:
      cpus: '0.25'
```

## Support and Maintenance

### Log Management

**View recent logs:**
```bash
docker logs --tail 50 pq-config-validator
```

**Follow logs in real-time:**
```bash
docker logs -f pq-config-validator
```

### Updates

**Update application:**
```bash
# Pull latest changes
git pull

# Rebuild and restart
./docker-deploy.sh stop
./docker-deploy.sh build
./docker-deploy.sh start
```

## Contact

**Author:** Ethan Li  
**Email:** <EMAIL>  
**Application:** PQ Config Validator v1.0.0

For technical support or questions about deployment, please contact the development team.
