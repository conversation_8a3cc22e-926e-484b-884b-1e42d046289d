#!/usr/bin/env python3
"""
Test script for upload directory configuration feature
Author: <PERSON> <<EMAIL>>
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path
import pytest

# Add the project root to Python path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent))

from utils import validate_upload_directory, FileProcessingError


def test_validate_upload_directory_creation():
    """Test upload directory creation with valid path"""
    with tempfile.TemporaryDirectory() as temp_dir:
        test_upload_dir = Path(temp_dir) / "test_uploads"
        
        result = validate_upload_directory(test_upload_dir)
        assert result.exists(), "Upload directory should be created"
        assert result.is_dir(), "Upload directory should be a directory"


def test_validate_upload_directory_existing():
    """Test upload directory validation with existing directory"""
    with tempfile.TemporaryDirectory() as temp_dir:
        test_upload_dir = Path(temp_dir) / "existing_uploads"
        test_upload_dir.mkdir()
        
        result = validate_upload_directory(test_upload_dir)
        assert result.exists(), "Existing directory should remain"
        assert result.is_dir(), "Directory should still be a directory"


def test_validate_upload_directory_invalid_parent():
    """Test upload directory validation with invalid parent directory"""
    invalid_dir = Path("/nonexistent/parent/uploads")
    
    with pytest.raises(FileProcessingError):
        validate_upload_directory(invalid_dir)


def test_config_environment_variable():
    """Test configuration with environment variable"""
    # Save original environment
    original_upload_dir = os.environ.get("UPLOAD_DIR")
    
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            custom_upload_dir = Path(temp_dir) / "custom_uploads"
            
            # Set environment variable
            os.environ["UPLOAD_DIR"] = str(custom_upload_dir)
            
            # Reload config module to pick up the environment variable
            import importlib
            import config
            importlib.reload(config)
            
            # Check if the config picked up the environment variable
            expected_path = custom_upload_dir.resolve()
            actual_path = config.UPLOAD_DIR.resolve()
            
            assert actual_path == expected_path, f"Expected {expected_path}, got {actual_path}"
                
    finally:
        # Restore original environment
        if original_upload_dir is not None:
            os.environ["UPLOAD_DIR"] = original_upload_dir
        elif "UPLOAD_DIR" in os.environ:
            del os.environ["UPLOAD_DIR"]
        
        # Reload config to restore original state
        import importlib
        import config
        importlib.reload(config)


def test_command_line_integration():
    """Test command-line argument integration"""
    # This test simulates what happens when --upload-dir is used
    with tempfile.TemporaryDirectory() as temp_dir:
        custom_upload_dir = Path(temp_dir) / "cli_uploads"
        
        # Simulate setting environment variable (as run.py would do)
        original_upload_dir = os.environ.get("UPLOAD_DIR")
        
        try:
            os.environ["UPLOAD_DIR"] = str(custom_upload_dir)
            
            # Test that the directory can be validated
            result = validate_upload_directory(custom_upload_dir)
            
            assert result.exists(), "Upload directory should be created"
            assert result.is_dir(), "Upload directory should be a directory"
                
        finally:
            # Restore original environment
            if original_upload_dir is not None:
                os.environ["UPLOAD_DIR"] = original_upload_dir
            elif "UPLOAD_DIR" in os.environ:
                del os.environ["UPLOAD_DIR"]


# For backward compatibility, provide a main function that runs pytest
def main():
    """Run all tests using pytest"""
    import subprocess
    import sys
    
    # Run pytest on this file
    result = subprocess.run([
        sys.executable, "-m", "pytest", __file__, "-v"
    ], capture_output=False)
    
    return result.returncode == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
