#!/usr/bin/env python3
"""
Startup script for DVV
Author: <PERSON> <<EMAIL>>
"""

import argparse
import sys
import os
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Import config values but delay main import until after environment setup
from config import HOST, PORT, LOG_LEVEL, APP_NAME, APP_VERSION

def main():
    parser = argparse.ArgumentParser(
        description=f"{APP_NAME} v{APP_VERSION}",
        epilog="""
Examples:
  python run.py                                    # Use default upload directory (./uploads)
  python run.py --upload-dir /custom/uploads       # Use custom upload directory
  python run.py --upload-dir C:\\MyUploads         # Windows path example
  python run.py --host 127.0.0.1 --port 8080      # Custom host and port
  python run.py --upload-dir /tmp/uploads --reload # Development mode with custom uploads
        """,
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    parser.add_argument("--host", default=HOST, help="Host to bind to")
    parser.add_argument("--port", type=int, default=PORT, help="Port to bind to")
    parser.add_argument("--log-level", default=LOG_LEVEL,
                       choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
                       help="Logging level")
    parser.add_argument("--upload-dir", type=str,
                       help="Custom directory for file uploads. The directory will be created if it doesn't exist. Parent directory must be writable. (default: ./uploads)")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload for development")

    args = parser.parse_args()

    # Set upload directory environment variable if provided
    if args.upload_dir:
        os.environ["UPLOAD_DIR"] = args.upload_dir

    # Import utilities and main app after environment is set
    from utils import setup_logging, create_safe_directory
    from main import app

    # Setup logging
    logs_dir = Path("logs")
    create_safe_directory(logs_dir)
    setup_logging(args.log_level, logs_dir)
    
    # Import uvicorn after logging is set up
    try:
        import uvicorn
    except ImportError:
        print("Error: uvicorn is not installed. Please install it with: pip install uvicorn")
        sys.exit(1)
    
    print(f"Starting DVV v{APP_VERSION}")
    print(f"Author: Ethan Li")
    print(f"Server will be available at http://{args.host}:{args.port}")
    print(f"Log level: {args.log_level}")

    # Display upload directory information
    if args.upload_dir:
        upload_path = Path(args.upload_dir).resolve()
        print(f"Upload directory: {upload_path}")
    else:
        default_upload_path = Path("uploads").resolve()
        print(f"Upload directory: {default_upload_path} (default)")
    
    # Run the server
    uvicorn.run(
        "main:app",
        host=args.host,
        port=args.port,
        log_level=args.log_level.lower(),
        reload=args.reload
    )

if __name__ == "__main__":
    main()
