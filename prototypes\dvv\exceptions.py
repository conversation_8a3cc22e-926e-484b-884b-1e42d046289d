"""
Custom exceptions for the Parameter Detection Tool
"""

class ParameterDetectionError(Exception):
    """Base exception for parameter detection errors"""
    pass

class FileProcessingError(ParameterDetectionError):
    """Exception raised when file processing fails"""
    pass

class SessionError(ParameterDetectionError):
    """Exception raised for session-related errors"""
    pass

class ValidationError(ParameterDetectionError):
    """Exception raised for validation errors"""
    pass

class ConfigurationError(ParameterDetectionError):
    """Exception raised for configuration errors"""
    pass

class FileUploadError(ParameterDetectionError):
    """Exception raised for file upload errors"""
    pass
