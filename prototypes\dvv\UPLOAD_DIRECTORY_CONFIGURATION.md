# Upload Directory Configuration

This document describes how to configure a custom upload directory for the DVV (Dolby Vision Validator) web application.

## Overview

By default, the DVV application stores uploaded files in a `./uploads` directory relative to the application's location. You can now specify a custom upload directory location using either command-line arguments or environment variables.

## Configuration Methods

### 1. Command-Line Argument (Recommended)

Use the `--upload-dir` argument when starting the application:

```bash
# Use a custom upload directory
python run.py --upload-dir /path/to/custom/uploads

# Windows example
python run.py --upload-dir C:\MyUploads

# Relative path example
python run.py --upload-dir ./custom_uploads

# Combined with other options
python run.py --upload-dir /tmp/uploads --host 127.0.0.1 --port 8080
```

### 2. Environment Variable

Set the `UPLOAD_DIR` environment variable before starting the application:

```bash
# Linux/macOS
export UPLOAD_DIR=/path/to/custom/uploads
python run.py

# Windows Command Prompt
set UPLOAD_DIR=C:\MyUploads
python run.py

# Windows PowerShell
$env:UPLOAD_DIR="C:\MyUploads"
python run.py
```

## Directory Requirements

The upload directory must meet the following requirements:

1. **Parent Directory Exists**: The parent directory of the specified upload directory must exist
2. **Write Permissions**: The parent directory must be writable by the application
3. **Auto-Creation**: The upload directory itself will be created automatically if it doesn't exist
4. **Write Access**: The application will verify write access by creating a test file

## Error Handling

The application will validate the upload directory during startup and provide clear error messages if there are issues:

- **Parent directory doesn't exist**: `Parent directory does not exist: /path/to/parent`
- **Permission denied**: `Parent directory is not writable: /path/to/parent`
- **Cannot write to directory**: `Cannot write to upload directory /path/to/uploads: Permission denied`

## Examples

### Basic Usage

```bash
# Start with default upload directory (./uploads)
python run.py

# Start with custom upload directory
python run.py --upload-dir /var/www/uploads
```

### Development Setup

```bash
# Development mode with custom uploads and auto-reload
python run.py --upload-dir ./dev_uploads --reload --log-level DEBUG
```

### Production Setup

```bash
# Production with dedicated upload directory
python run.py --upload-dir /opt/dvv/uploads --host 0.0.0.0 --port 8000
```

### Docker/Container Usage

```bash
# Using environment variable in Docker
docker run -e UPLOAD_DIR=/app/uploads -v /host/uploads:/app/uploads dvv-app

# Using command-line argument
docker run dvv-app python run.py --upload-dir /app/uploads
```

## Directory Structure

When using a custom upload directory, the application maintains the same internal structure:

```
custom_upload_directory/
├── user1/
│   ├── 20250127_120000_config.txt
│   └── log_outputs/
│       └── 20250127_120000/
│           └── analysis_results.png
├── user2/
│   └── 20250127_130000_firmware.log
└── .write_test (temporary file for validation)
```

## Security Considerations

1. **Path Validation**: The application validates and resolves all paths to prevent directory traversal attacks
2. **Permission Checks**: Write permissions are verified before accepting uploads
3. **Sanitized Filenames**: All uploaded filenames are sanitized to prevent security issues
4. **User Isolation**: Each user gets their own subdirectory within the upload directory

## Troubleshooting

### Common Issues

1. **Permission Denied**
   - Ensure the parent directory is writable
   - Check file system permissions
   - On Linux/macOS, you may need to use `chmod` to set proper permissions

2. **Directory Not Found**
   - Verify the parent directory exists
   - Use absolute paths to avoid confusion
   - Check for typos in the path

3. **Disk Space**
   - Ensure sufficient disk space is available
   - Monitor upload directory size in production

### Testing Configuration

You can test the upload directory configuration using the provided test script:

```bash
python test_upload_directory.py
```

This will run comprehensive tests to verify:
- Directory validation functionality
- Environment variable configuration
- Command-line argument integration

## Help and Support

For additional help, use the built-in help:

```bash
python run.py --help
```

This will display all available options including the upload directory configuration.
