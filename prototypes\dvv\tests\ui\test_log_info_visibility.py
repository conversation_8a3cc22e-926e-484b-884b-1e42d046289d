"""
Test for Log File Information section visibility

This test verifies that the "Log File Information" section is correctly
shown only for parameter validation mode and hidden for log visualization mode.
This ensures better maintainability by using processing_mode instead of specific processor names.
"""

import pytest
import re
from pathlib import Path

class TestLogInfoVisibility:
    """Test Log File Information section visibility in JavaScript frontend"""

    def test_javascript_condition_logic(self):
        """Test that the JavaScript condition correctly uses processing_mode for better maintainability"""

        # Read the JavaScript file
        js_file_path = Path(__file__).parent.parent.parent / "static/js/app.js"
        assert js_file_path.exists(), "JavaScript file not found"

        with open(js_file_path, 'r', encoding='utf-8') as f:
            js_content = f.read()

        # Find the log info condition - should now use processing_mode
        log_info_pattern = r"if\s*\(\s*results\.log_info\s*&&\s*results\.processing_mode\s*===\s*['\"]parameter_validation['\"]\s*\)"

        matches = re.search(log_info_pattern, js_content)
        assert matches is not None, "Log info condition not found or incorrect in JavaScript. Expected to use processing_mode === 'parameter_validation'"

        print("✓ JavaScript condition correctly uses processing_mode for parameter validation")
    
    def test_comment_accuracy(self):
        """Test that the comment accurately describes the condition"""

        js_file_path = Path(__file__).parent.parent.parent / "static/js/app.js"
        with open(js_file_path, 'r', encoding='utf-8') as f:
            js_content = f.read()

        # Find the comment before the condition - should now mention parameter validation mode
        comment_pattern = r"//\s*Display log file information \(only for parameter validation mode\)"

        matches = re.search(comment_pattern, js_content)
        assert matches is not None, "Comment not found or incorrect in JavaScript. Expected to mention 'only for parameter validation mode'"

        print("✓ Comment accurately describes showing log info only for parameter validation mode")
    
    def test_processing_mode_logic(self):
        """Test the logical behavior of processing mode based visibility"""

        # Simulate the JavaScript condition logic in Python
        def should_show_log_info(processing_mode, has_log_info=True):
            """Simulate the JavaScript condition using processing_mode"""
            return has_log_info and processing_mode == 'parameter_validation'

        # Test cases
        test_cases = [
            # (processing_mode, has_log_info, expected_result, description)
            ('parameter_validation', True, True, 'Parameter validation mode should show log info'),
            ('log_visualization', True, False, 'Log visualization mode should hide log info'),
            ('parameter_validation', False, False, 'No log info available should hide section'),
            ('log_visualization', False, False, 'No log info available should hide section'),
            ('unknown_mode', True, False, 'Unknown processing mode should hide log info'),
        ]

        for mode, has_log_info, expected, description in test_cases:
            result = should_show_log_info(mode, has_log_info)
            assert result == expected, f"Failed: {description} (got {result}, expected {expected})"
            print(f"✓ {description}")

    def test_maintainability_improvement(self):
        """Test that the new approach is more maintainable"""

        # Simulate adding new processors to log visualization mode
        def should_show_log_info_new_approach(processing_mode, has_log_info=True):
            """New approach using processing_mode"""
            return has_log_info and processing_mode == 'parameter_validation'

        def should_show_log_info_old_approach(processor_used, has_log_info=True):
            """Old approach using specific processor names"""
            return has_log_info and processor_used != 'grid_comparison' and processor_used != 'firmware_log_visualizer'

        # Test with new hypothetical processors
        test_cases = [
            # (processor_used, processing_mode, description)
            ('new_log_processor', 'log_visualization', 'New log processor in log visualization mode'),
            ('another_log_tool', 'log_visualization', 'Another log tool in log visualization mode'),
            ('future_processor', 'log_visualization', 'Future processor in log visualization mode'),
        ]

        for processor, mode, description in test_cases:
            # New approach should correctly hide log info for any processor in log_visualization mode
            new_result = should_show_log_info_new_approach(mode, True)
            assert new_result == False, f"New approach failed for {description}"

            # Old approach would incorrectly show log info for new processors
            old_result = should_show_log_info_old_approach(processor, True)
            assert old_result == True, f"Old approach would incorrectly show log info for {description}"

            print(f"✓ New approach correctly handles {description}")

        print("✓ New processing_mode approach is more maintainable than processor-specific checks")
    
    def test_consistency_with_main_logic(self):
        """Test that the log info visibility logic is consistent with main processing mode logic"""

        # Read the JavaScript file
        js_file_path = Path(__file__).parent.parent.parent / "static/js/app.js"
        with open(js_file_path, 'r', encoding='utf-8') as f:
            js_content = f.read()

        # Check that both the main processing mode check and log info check use the same pattern
        main_mode_pattern = r"results\.processing_mode\s*===\s*['\"]log_visualization['\"]\s*"
        log_info_pattern = r"results\.processing_mode\s*===\s*['\"]parameter_validation['\"]\s*"

        main_matches = re.search(main_mode_pattern, js_content)
        log_info_matches = re.search(log_info_pattern, js_content)

        assert main_matches is not None, "Main processing mode check not found"
        assert log_info_matches is not None, "Log info processing mode check not found"

        print("✓ Both main logic and log info logic consistently use processing_mode")

        # Verify the logic is complementary (one checks for log_visualization, other for parameter_validation)
        assert 'log_visualization' in main_matches.group(), "Main logic should check for log_visualization"
        assert 'parameter_validation' in log_info_matches.group(), "Log info logic should check for parameter_validation"

        print("✓ Logic correctly uses complementary processing modes")
    
    def test_backward_compatibility(self):
        """Test that the refactoring doesn't break existing functionality"""

        # Simulate results for different scenarios
        test_scenarios = [
            {
                'name': 'Parameter validation with log info',
                'results': {
                    'processing_mode': 'parameter_validation',
                    'log_info': {'total_frames': 100},
                    'processor_used': 'pq_config_validator'
                },
                'should_show_log_info': True
            },
            {
                'name': 'Log visualization with grid comparison',
                'results': {
                    'processing_mode': 'log_visualization',
                    'log_info': {'total_frames': 100},
                    'processor_used': 'grid_comparison'
                },
                'should_show_log_info': False
            },
            {
                'name': 'Log visualization with firmware log visualizer',
                'results': {
                    'processing_mode': 'log_visualization',
                    'log_info': {'total_frames': 100},
                    'processor_used': 'firmware_log_visualizer'
                },
                'should_show_log_info': False
            },
            {
                'name': 'Parameter validation without log info',
                'results': {
                    'processing_mode': 'parameter_validation',
                    'log_info': None,
                    'processor_used': 'pq_config_validator'
                },
                'should_show_log_info': False
            }
        ]

        # Test the new logic
        def should_show_log_info_new(results):
            return bool(results.get('log_info') and results.get('processing_mode') == 'parameter_validation')

        for scenario in test_scenarios:
            result = should_show_log_info_new(scenario['results'])
            expected = scenario['should_show_log_info']
            assert result == expected, f"Failed for scenario: {scenario['name']} (got {result}, expected {expected})"
            print(f"✓ {scenario['name']}: {'Shows' if result else 'Hides'} log info as expected")

        print("✓ All backward compatibility scenarios pass")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
