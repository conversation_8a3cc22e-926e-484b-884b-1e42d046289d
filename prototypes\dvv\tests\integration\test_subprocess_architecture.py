#!/usr/bin/env python3
"""
Comprehensive test suite for the updated subprocess-based architecture

Tests all three applications and their specific workflows:
1. PQ Config Validator (parameter validation mode)
2. Grid Comparison Plotter (log visualization with grid layout)
3. Firmware Log Visualizer (single file firmware log analysis)

Note: log_parser_plotter is a utility module used by grid_comparison_plotter, not a standalone application.
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import patch, MagicMock
import json
import os

# Import modules to test
import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from log_processors import (
    TCFlashLogDetector, FirmwareLogDetector, SubprocessLogProcessor,
    LogProcessorManager, LogProcessingResult
)
from main import process_log_file
from web_app_registry import web_app_registry

class TestTCFlashLogDetector:
    """Test TC Flash log detection functionality"""
    
    def test_valid_tc_flash_log(self, tmp_path):
        """Test detection of valid TC Flash log file"""
        log_content = """
fallback: 0
Config values:
intensity_level: 100
gain_red: 50
gain_green: 60
gain_blue: 70

DM values:
LocalMappingStrength 80
UpMappingStrength 90

fallback: 0
Config values:
intensity_level: 105
gain_red: 55
gain_green: 65
gain_blue: 75

DM values:
LocalMappingStrength 85
UpMappingStrength 95
"""
        log_file = tmp_path / "test_log.txt"
        log_file.write_text(log_content)
        
        assert TCFlashLogDetector.is_tc_flash_log(log_file) == True
    
    def test_invalid_log_file(self, tmp_path):
        """Test detection of invalid log file"""
        invalid_content = "This is not a TC Flash log file"
        log_file = tmp_path / "invalid.txt"
        log_file.write_text(invalid_content)
        
        assert TCFlashLogDetector.is_tc_flash_log(log_file) == False
    
    def test_nonexistent_file(self, tmp_path):
        """Test detection of nonexistent file"""
        log_file = tmp_path / "nonexistent.txt"
        
        assert TCFlashLogDetector.is_tc_flash_log(log_file) == False


class TestFirmwareLogDetector:
    """Test Firmware log detection functionality in integration context"""

    def test_valid_firmware_log_integration(self, tmp_path):
        """Test detection of valid firmware log file in integration context"""
        firmware_content = """
        L17-IC-filter_0.0416.txt firmware log content
        Debug output from Dolby Vision processing

        dBrightness(100)
        dContrast(85)
        dSaturation(90)
        dBacklight(75)

        gainPos_precisionRendering(50)
        gainPos_dLocalContrast(60)
        gainPos_dBrightness(45)

        confidence(95)
        precisionRenderingStrength(80)

        mid_boost: 40
        highlight_stretch: 35
        shadow_drop: 20
        """
        log_file = tmp_path / "test_firmware_log.txt"
        log_file.write_text(firmware_content)

        assert FirmwareLogDetector.is_firmware_log(log_file) == True

        # Test log info extraction
        info = FirmwareLogDetector.get_log_info(log_file)
        assert info['total_measurements'] > 0
        assert info['estimated_processable'] == True
        assert info['dolby_parameters'] > 0
        assert info['level_parameters'] > 0

    def test_tc_flash_log_detected_as_firmware_log(self, tmp_path):
        """Test that TC Flash logs are also detected as firmware logs"""
        tc_flash_content = """
        fallback: 0
        Config values:
        intensity_level: 100
        gain_red: 50
        gain_green: 60
        gain_blue: 70

        DM values:
        LocalMappingStrength 80
        UpMappingStrength 90

        fallback: 0
        Config values:
        intensity_level: 105
        gain_red: 55
        gain_green: 65
        gain_blue: 75
        """
        log_file = tmp_path / "test_tc_flash.txt"
        log_file.write_text(tc_flash_content)

        # Both detectors should return True
        assert TCFlashLogDetector.is_tc_flash_log(log_file) == True
        assert FirmwareLogDetector.is_firmware_log(log_file) == True

    def test_invalid_firmware_log_integration(self, tmp_path):
        """Test detection of invalid firmware log file"""
        invalid_content = "This is not a firmware log file with no parameters"
        log_file = tmp_path / "invalid.txt"
        log_file.write_text(invalid_content)

        assert FirmwareLogDetector.is_firmware_log(log_file) == False

    def test_nonexistent_firmware_log(self, tmp_path):
        """Test detection of nonexistent firmware log file"""
        log_file = tmp_path / "nonexistent.txt"

        assert FirmwareLogDetector.is_firmware_log(log_file) == False


class TestSubprocessLogProcessor:
    """Test subprocess-based log processing functionality"""
    
    def test_get_bin_directory(self):
        """Test bin directory detection"""
        bin_dir = SubprocessLogProcessor.get_bin_directory()
        assert bin_dir.exists()
        assert bin_dir.name == "bin"
        
        # Check that the required scripts exist
        grid_script = bin_dir / "grid_comparison_plotter.py"
        log_script = bin_dir / "log_parser_plotter.py"
        assert grid_script.exists(), f"grid_comparison_plotter.py not found in {bin_dir}"
        assert log_script.exists(), f"log_parser_plotter.py not found in {bin_dir}"
    
    @patch('subprocess.run')
    def test_execute_grid_comparison_plotter(self, mock_run, tmp_path):
        """Test grid comparison plotter execution"""
        # Setup mock
        mock_run.return_value = MagicMock(returncode=0, stdout="", stderr="")
        
        # Create test input file
        log_content = """
fallback: 0
Config values:
intensity_level: 100
gain_red: 50
gain_green: 60
gain_blue: 70

DM values:
LocalMappingStrength 80
UpMappingStrength 90
"""
        input_file = tmp_path / "test_log.txt"
        input_file.write_text(log_content)
        
        output_dir = tmp_path / "output"
        output_dir.mkdir()
        
        # Create expected output file
        expected_output = output_dir / "all_parameters_comparison_grid.png"
        expected_output.write_bytes(b"fake png data")
        
        processor = SubprocessLogProcessor()
        result = processor.execute_grid_comparison_plotter(input_file, output_dir)
        
        assert result.success == True
        assert result.processor_used == "grid_comparison"
        assert len(result.output_files) == 1
        assert "all_parameters_comparison_grid.png" in result.output_files[0]
        
        # Verify subprocess was called with correct arguments
        mock_run.assert_called_once()
        call_args = mock_run.call_args[0][0]  # Get the command list
        assert "grid_comparison_plotter.py" in str(call_args[1])
        assert str(input_file) in call_args
        assert "--output-dir" in call_args
        assert str(output_dir) in call_args
    
    @patch('subprocess.run')
    def test_execute_firmware_log_visualizer(self, mock_run, tmp_path):
        """Test firmware log visualizer execution"""
        # Setup mock
        mock_run.return_value = MagicMock(returncode=0, stdout="", stderr="")

        # Create test input file
        firmware_log_content = """
L17-IC-filter_0.0416.txt firmware log content
Some debug output with numerical parameters
intensity_level: 100
gain_red: 50.5
gain_green: 60.2
gain_blue: 70.8
"""
        input_file = tmp_path / "L17-IC-filter_0.0416.txt"
        input_file.write_text(firmware_log_content)

        output_dir = tmp_path / "output"
        output_dir.mkdir()

        # Create expected output files
        expected_png = output_dir / "L17-IC-filter_0.0416.png"
        expected_csv = output_dir / "summary.csv"
        expected_png.write_bytes(b"fake png data")
        expected_csv.write_text("parameter,count,mean,std\nintensity_level,1,100,0")

        processor = SubprocessLogProcessor()
        result = processor.execute_firmware_log_visualizer(input_file, output_dir)

        assert result.success == True
        assert result.processor_used == "firmware_log_visualizer"
        assert len(result.output_files) == 2  # PNG and CSV
        assert "L17-IC-filter_0.0416.png" in result.output_files
        assert "summary.csv" in result.output_files

        # Verify command was called correctly with --output and --no-show
        mock_run.assert_called_once()
        called_args = mock_run.call_args[0][0]
        assert "firmware_log_visualizer.py" in called_args[1]
        assert str(input_file) in called_args
        assert "--output" in called_args
        assert str(output_dir) in called_args
        assert "--no-show" in called_args

class TestLogProcessorManager:
    """Test log processor manager functionality"""
    
    def test_process_with_user_selection_grid_comparison(self, tmp_path):
        """Test processing with user selection for grid comparison"""
        log_content = """
fallback: 0
Config values:
intensity_level: 100
gain_red: 50
gain_green: 60
gain_blue: 70

DM values:
LocalMappingStrength 80
UpMappingStrength 90
"""
        log_file = tmp_path / "test_log.txt"
        log_file.write_text(log_content)
        
        manager = LogProcessorManager()
        
        # Mock the subprocess execution
        with patch.object(SubprocessLogProcessor, 'execute_grid_comparison_plotter') as mock_execute:
            mock_result = LogProcessingResult(
                success=True,
                message="Grid comparison visualization generated successfully",
                processor_used="grid_comparison",
                output_files=["all_parameters_comparison_grid.png"],
                static_files=[{
                    "filename": "all_parameters_comparison_grid.png",
                    "unique_filename": "20250127_143022_all_parameters_comparison_grid.png",
                    "url": "/static/images/grid_comparison/20250127_143022_all_parameters_comparison_grid.png"
                }]
            )
            mock_execute.return_value = mock_result
            
            output_dir = tmp_path / "output"
            result = manager.process_log_file(log_file, output_dir, "grid_comparison")
            
            assert result.success == True
            assert result.processor_used == "grid_comparison"
            assert len(result.static_files) == 1
            mock_execute.assert_called_once()
    
    def test_process_with_user_selection_firmware_log_visualizer(self, tmp_path):
        """Test processing with user selection for firmware log visualizer"""
        log_content = """
L17-IC-filter_0.0416.txt firmware log content
Debug output from Dolby Vision processing

dBrightness(100)
dContrast(85)
dSaturation(90)
dBacklight(75)

gainPos_precisionRendering(50)
gainPos_dLocalContrast(60)
gainPos_dBrightness(45)

confidence(95)
precisionRenderingStrength(80)

mid_boost: 40
highlight_stretch: 35
shadow_drop: 20
"""
        log_file = tmp_path / "L17-IC-filter_0.0416.txt"
        log_file.write_text(log_content)

        manager = LogProcessorManager()

        # Mock the subprocess execution
        with patch.object(SubprocessLogProcessor, 'execute_firmware_log_visualizer') as mock_execute:
            mock_result = LogProcessingResult(
                success=True,
                message="Firmware log visualization generated successfully",
                processor_used="firmware_log_visualizer",
                output_files=["L17-IC-filter_0.0416.png", "summary.csv"],
                static_files=[{
                    "filename": "L17-IC-filter_0.0416.png",
                    "unique_filename": "20250127_143022_L17-IC-filter_0.0416.png",
                    "url": "/static/images/firmware_log_visualizer/20250127_143022_L17-IC-filter_0.0416.png"
                }]
            )
            mock_execute.return_value = mock_result

            output_dir = tmp_path / "output"
            result = manager.process_log_file(log_file, output_dir, "firmware_log_visualizer")

            assert result.success == True
            assert result.processor_used == "firmware_log_visualizer"
            assert len(result.output_files) == 2
            assert len(result.static_files) == 1  # Only PNG files are copied to static
            mock_execute.assert_called_once()

class TestWebApplicationRegistry:
    """Test web application registry for three-application logic"""

    def test_get_enabled_applications(self):
        """Test that all three applications are enabled"""
        enabled_apps = web_app_registry.get_enabled_applications()

        # Should have exactly 3 enabled applications
        assert len(enabled_apps) == 3

        # Check specific applications
        app_ids = [app.id for app in enabled_apps]
        expected_ids = ["pq_config_validator", "grid_comparison_plotter", "firmware_log_visualizer"]

        for expected_id in expected_ids:
            assert expected_id in app_ids, f"Application {expected_id} not found in enabled applications"
    
    def test_application_properties(self):
        """Test that applications have correct properties"""
        enabled_apps = web_app_registry.get_enabled_applications()
        
        for app in enabled_apps:
            assert hasattr(app, 'id')
            assert hasattr(app, 'name')
            assert hasattr(app, 'description')
            assert hasattr(app, 'icon')
            assert hasattr(app, 'url')
            assert hasattr(app, 'enabled')
            assert app.enabled == True


