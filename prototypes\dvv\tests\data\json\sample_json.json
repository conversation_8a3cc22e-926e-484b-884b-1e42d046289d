{"application": {"name": "Parameter Detection Test", "version": "1.0.0", "environment": "{env_name}", "debug": "{debug_mode}"}, "server": {"host": "localhost", "port": 8080, "ssl": {"enabled": "{ssl_enabled}", "certificate": "{ssl_cert_path}", "private_key": "{ssl_key_path}"}}, "database": {"type": "postgresql", "host": "{db_host}", "port": "{db_port}", "name": "testdb", "credentials": {"username": "{db_username}", "password": "{db_password}"}, "pool": {"min_connections": "{db_pool_min}", "max_connections": "{db_pool_max}"}}, "logging": {"level": "INFO", "file": "{log_file_path}", "format": "{log_format}", "rotation": {"enabled": "{log_rotation_enabled}", "max_size": "{log_max_size}", "backup_count": "{log_backup_count}"}}, "features": {"caching": {"enabled": "{cache_enabled}", "type": "{cache_type}", "ttl": "{cache_ttl}"}, "monitoring": {"enabled": "{monitoring_enabled}", "endpoint": "{monitoring_endpoint}", "api_key": "{monitoring_api_key}"}}, "external_services": {"email": {"smtp_host": "{smtp_host}", "smtp_port": "{smtp_port}", "username": "{smtp_username}", "password": "{smtp_password}"}, "storage": {"type": "{storage_type}", "bucket": "{storage_bucket}", "region": "{storage_region}", "access_key": "{storage_access_key}", "secret_key": "{storage_secret_key}"}}}