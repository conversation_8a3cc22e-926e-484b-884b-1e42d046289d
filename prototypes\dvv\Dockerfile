# PQ Config Validator Dockerfile
# Author: <PERSON>
# Multi-stage build for optimized production image

# Build stage
FROM python:3.11-slim as builder

# Set environment variables for build
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies for building
RUN apt-get update && apt-get install -y \
    build-essential \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Create and activate virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --upgrade pip && \
    pip install -r requirements.txt

# Production stage
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PATH="/opt/venv/bin:$PATH" \
    PYTHONPATH="/app" \
    HOST=0.0.0.0 \
    PORT=8000 \
    LOG_LEVEL=INFO \
    DEBUG=false

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Copy virtual environment from builder stage
COPY --from=builder /opt/venv /opt/venv

# Create application user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Create application directory
WORKDIR /app

# Create necessary directories with proper permissions
RUN mkdir -p /app/uploads /app/sessions /app/logs /app/static /app/templates /app/bin && \
    chown -R appuser:appuser /app

# Copy application files with proper ownership
COPY --chown=appuser:appuser . .

# Ensure external tool is executable (if present)
RUN if [ -f "/app/bin/dtv_config_lib_test.exe" ]; then \
        chmod +x /app/bin/dtv_config_lib_test.exe; \
    fi

# Create startup script
RUN echo '#!/bin/bash\n\
echo "=== PQ Config Validator v1.0.0 ==="\n\
echo "Author: Ethan Li"\n\
echo "Container: $(hostname)"\n\
echo "Python: $(python --version)"\n\
echo "Working directory: $(pwd)"\n\
echo "Starting server on ${HOST}:${PORT}"\n\
echo "Log level: ${LOG_LEVEL}"\n\
echo "Debug mode: ${DEBUG}"\n\
echo "==================================="\n\
exec python main.py' > /app/start.sh && \
    chmod +x /app/start.sh && \
    chown appuser:appuser /app/start.sh

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:${PORT:-8000}/ || exit 1

# Default command
CMD ["/app/start.sh"]
