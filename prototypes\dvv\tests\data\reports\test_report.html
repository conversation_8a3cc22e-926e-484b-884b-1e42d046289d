<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PQ Config Validator Report</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 40px; background-color: #f8f9fa; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; border-bottom: 3px solid #007bff; padding-bottom: 20px; margin-bottom: 30px; }
        .header h1 { color: #007bff; margin: 0; font-size: 2.5em; }
        .header p { color: #6c757d; margin: 5px 0; }
        .metadata { background: #e9ecef; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .metadata table { width: 100%; border-collapse: collapse; }
        .metadata td { padding: 5px 10px; border-bottom: 1px solid #dee2e6; }
        .metadata td:first-child { font-weight: bold; width: 150px; }
        .summary { background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .summary.error { background: #f8d7da; border-color: #f5c6cb; }
        .section { margin-bottom: 30px; }
        .section h2 { color: #495057; border-bottom: 2px solid #dee2e6; padding-bottom: 10px; }
        .parameter-item { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin-bottom: 10px; border-radius: 5px; }
        .warning-item { background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin-bottom: 10px; border-radius: 5px; }
        .line-number { background: #007bff; color: white; padding: 2px 8px; border-radius: 3px; font-size: 0.9em; }
        .code { font-family: 'Courier New', monospace; background: #f8f9fa; padding: 10px; border-left: 3px solid #007bff; margin-top: 10px; }
        .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6; color: #6c757d; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>PQ Config Validator</h1>
            <p>Analysis Report by Ethan Li</p>
            <p>Generated: 2025-07-23 22:33:56</p>
        </div>

        <div class="metadata">
            <table>
                <tr><td>User ID:</td><td>test_download_user</td></tr>
                <tr><td>Analyzed File:</td><td>test_config.cfg</td></tr>
                <tr><td>External Tool Used:</td><td>Yes</td></tr>
                <tr><td>Tool Version:</td><td>1.0.0</td></tr>
            </table>
        </div>

        <div class="summary ">
            <h3>Analysis Summary</h3>
            <p><strong>Status:</strong> Success</p>
            <p><strong>Message:</strong> Analysis complete. Found 3 undefined parameter(s) and 8 validation warning(s).</p>
            <p><strong>Undefined Parameters:</strong> 3</p>
            <p><strong>Validation Warnings:</strong> 8</p>
        </div>
        <div class="section">
            <h2>Undefined Parameters</h2>
            <div class="parameter-item">
                <h4>1. Parameter: <code>ssl_setting</code>
                    <span class="line-number">Line 6</span>
                </h4>
                <div class="code">ssl_enabled = {ssl_setting}  # undefined</div>
            </div>
            <div class="parameter-item">
                <h4>2. Parameter: <code>db_url</code>
                    <span class="line-number">Line 7</span>
                </h4>
                <div class="code">database_url = {db_url}      # undefined</div>
            </div>
            <div class="parameter-item">
                <h4>3. Parameter: <code>secret_key</code>
                    <span class="line-number">Line 8</span>
                </h4>
                <div class="code">api_key = {secret_key}       # undefined</div>
            </div></div>
        <div class="section">
            <h2>Validation Warnings</h2>
            <div class="warning-item">
                <h4>1. Validation Warning</h4>
                <p><strong>Message:</strong> Couldn't find section 'Global'</p></div>
            <div class="warning-item">
                <h4>2. Validation Warning</h4>
                <p><strong>Message:</strong> Couldn't find section 'PictureMode=0'</p></div>
            <div class="warning-item">
                <h4>3. Validation Warning</h4>
                <p><strong>Message:</strong> Invalid Version number . Must be in the format <MAJOR>.<MINOR></p></div>
            <div class="warning-item">
                <h4>4. Validation Warning</h4>
                <p><strong>Message:</strong> Setting DM version to 4.0</p></div>
            <div class="warning-item">
                <h4>5. Validation Warning</h4>
                <p><strong>Message:</strong> Couldn't find section 'Global'</p></div>
            <div class="warning-item">
                <h4>6. Validation Warning</h4>
                <p><strong>Message:</strong> Couldn't find section 'PictureMode=0'</p></div>
            <div class="warning-item">
                <h4>7. Validation Warning</h4>
                <p><strong>Message:</strong> Invalid Version number . Must be in the format <MAJOR>.<MINOR></p></div>
            <div class="warning-item">
                <h4>8. Validation Warning</h4>
                <p><strong>Message:</strong> Setting DM version to 4.0</p></div></div>
        <div class="footer">
            <p>&copy; 2025 PQ Config Validator by Ethan Li - Dolby Laboratories</p>
        </div>
    </div>
</body>
</html>