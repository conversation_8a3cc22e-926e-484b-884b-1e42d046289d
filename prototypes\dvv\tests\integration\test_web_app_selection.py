#!/usr/bin/env python3
"""
Test script for web application selection functionality
"""

import json
import sys
from pathlib import Path

# Add parent directory to path to import local modules
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from web_app_registry import web_app_registry
from config import WEB_APPLICATIONS

def test_web_app_registry():
    """Test the web application registry functionality"""
    print("Testing Web Application Registry...")
    
    # Test getting enabled applications
    enabled_apps = web_app_registry.get_enabled_applications()
    print(f"[OK] Found {len(enabled_apps)} enabled applications:")
    for app in enabled_apps:
        print(f"  - {app.name} ({app.id}): {app.description}")
    
    # Test getting specific application
    pq_app = web_app_registry.get_application("pq_config_validator")
    if pq_app:
        print(f"[OK] PQ Config Validator found: {pq_app.name}")
    else:
        print("[FAIL] PQ Config Validator not found")
    
    # Test validation
    is_valid = web_app_registry.is_valid_application("pq_config_validator")
    print(f"[OK] PQ Config Validator is valid: {is_valid}")
    
    # Test invalid application
    is_invalid = web_app_registry.is_valid_application("nonexistent_app")
    print(f"[OK] Nonexistent app is invalid: {not is_invalid}")
    
    # Test URL generation
    url = web_app_registry.get_application_url("pq_config_validator", "test_user")
    print(f"[OK] PQ Config Validator URL: {url}")
    
    print("Web Application Registry tests completed successfully!\n")

def test_config():
    """Test the configuration"""
    print("Testing Configuration...")
    
    print(f"[OK] Found {len(WEB_APPLICATIONS)} applications in config:")
    for app_id, app_config in WEB_APPLICATIONS.items():
        print(f"  - {app_id}: {app_config['name']}")
        print(f"    Enabled: {app_config['enabled']}")
        print(f"    URL: {app_config['url']}")
    
    print("Configuration tests completed successfully!\n")

def test_json_serialization():
    """Test JSON serialization of application data"""
    print("Testing JSON Serialization...")
    
    apps = web_app_registry.get_enabled_applications()
    app_data = [
        {
            "id": app.id,
            "name": app.name,
            "description": app.description,
            "icon": app.icon,
            "url": app.url,
            "author": app.author,
            "version": app.version
        }
        for app in apps
    ]
    
    try:
        json_str = json.dumps(app_data, indent=2)
        print("[OK] JSON serialization successful:")
        print(json_str)
    except Exception as e:
        print(f"[FAIL] JSON serialization failed: {e}")
    
    print("JSON serialization tests completed successfully!\n")

if __name__ == "__main__":
    print("=" * 60)
    print("Web Application Selection Feature Test")
    print("=" * 60)
    
    try:
        test_config()
        test_web_app_registry()
        test_json_serialization()
        
        print("=" * 60)
        print("All tests passed! [OK]")
        print("=" * 60)
        
    except Exception as e:
        print(f"Test failed with error: {e}")
        sys.exit(1)
