# Deployment Guide

This guide covers different deployment options for the Parameter Detection Tool on a local area network (LAN).

## Quick Start (Windows)

1. **Download/clone the project files**
2. **Run the deployment script:**
   ```cmd
   deploy.bat
   ```
3. **Access the application** at the displayed network address

## Quick Start (Linux/macOS)

1. **Download/clone the project files**
2. **Make the script executable and run:**
   ```bash
   chmod +x deploy.sh
   ./deploy.sh
   ```
3. **Access the application** at the displayed network address

## Manual Deployment

### Prerequisites

- Python 3.8 or later
- pip (Python package installer)

### Steps

1. **Create virtual environment:**
   ```bash
   python -m venv venv
   ```

2. **Activate virtual environment:**
   - Windows: `venv\Scripts\activate`
   - Linux/macOS: `source venv/bin/activate`

3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Start the server:**
   ```bash
   python run.py --host 0.0.0.0 --port 8000
   ```

## Docker Deployment

### Using Docker Compose (Recommended)

1. **Build and start:**
   ```bash
   docker-compose up -d
   ```

2. **View logs:**
   ```bash
   docker-compose logs -f
   ```

3. **Stop:**
   ```bash
   docker-compose down
   ```

### Using Docker directly

1. **Build image:**
   ```bash
   docker build -t parameter-detection-tool .
   ```

2. **Run container:**
   ```bash
   docker run -d -p 8000:8000 \
     -v $(pwd)/uploads:/app/uploads \
     -v $(pwd)/data:/app/data \
     -v $(pwd)/logs:/app/logs \
     parameter-detection-tool
   ```

## Network Configuration

### Firewall Settings

Ensure port 8000 is open on the host machine:

**Windows:**
```cmd
netsh advfirewall firewall add rule name="Parameter Detection Tool" dir=in action=allow protocol=TCP localport=8000
```

**Linux (ufw):**
```bash
sudo ufw allow 8000
```

**Linux (iptables):**
```bash
sudo iptables -A INPUT -p tcp --dport 8000 -j ACCEPT
```

### Finding Your IP Address

**Windows:**
```cmd
ipconfig
```

**Linux/macOS:**
```bash
ip addr show
# or
ifconfig
```

## Environment Variables

Configure the application using environment variables:

| Variable | Default | Description |
|----------|---------|-------------|
| `HOST` | `0.0.0.0` | Server host address |
| `PORT` | `8000` | Server port |
| `LOG_LEVEL` | `INFO` | Logging level |
| `DEBUG` | `false` | Debug mode |
| `SESSION_TIMEOUT_HOURS` | `24` | Session timeout |

## Production Considerations

### Security

1. **Use HTTPS** in production with a reverse proxy (nginx/Apache)
2. **Set up proper authentication** if needed
3. **Configure firewall** to restrict access
4. **Regular security updates**

### Performance

1. **Use a production WSGI server** like Gunicorn:
   ```bash
   pip install gunicorn
   gunicorn -w 4 -k uvicorn.workers.UvicornWorker main:app --bind 0.0.0.0:8000
   ```

2. **Configure reverse proxy** for static files
3. **Set up monitoring** and logging
4. **Regular backups** of user data

### Monitoring

1. **Application logs** are stored in `logs/app.log`
2. **Session data** is stored in `data/sessions.json`
3. **Uploaded files** are stored in `uploads/`

## Troubleshooting

### Common Issues

1. **Port already in use:**
   - Change the port: `python run.py --port 8001`

2. **Permission denied:**
   - Run as administrator/sudo or change port to > 1024

3. **Cannot access from other machines:**
   - Check firewall settings
   - Ensure host is set to `0.0.0.0`
   - Verify network connectivity

4. **File upload fails:**
   - Check file size limits
   - Verify file permissions
   - Check available disk space

### Logs

Check application logs for detailed error information:
- Location: `logs/app.log`
- Increase verbosity: `--log-level DEBUG`

## Support

For issues and questions, check the application logs and ensure all prerequisites are met.
