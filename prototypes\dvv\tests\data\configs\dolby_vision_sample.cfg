# DolbyVision Configuration Sample
# This file is designed to test both parameter detection and external tool validation

# Defined parameters
display_name = "Sample TV"
panel_type = "LCD"
max_luminance = 1000
min_luminance = 0.01

# Configuration sections with undefined parameters
[display]
name = {display_name}
type = {panel_type}
peak_brightness = {max_luminance}
black_level = {min_luminance}
color_gamut = {color_space}  # This parameter is not defined
backlight_type = {backlight}  # This parameter is not defined

[color_processing]
# This section contains values that may trigger external tool warnings
Bx_color_primary = 0.154700  # This value is outside supported range
By_color_primary = 0.640000
Gx_color_primary = 0.300000
Gy_color_primary = 0.600000
Rx_color_primary = 0.708000
Ry_color_primary = 0.292000
Wx_color_primary = 0.3127
Wy_color_primary = 0.3290

# More undefined parameters
target_display_gamma = {gamma_value}  # gamma_value is not defined
color_temperature = {white_point}     # white_point is not defined
hdr_mode = {hdr_setting}             # hdr_setting is not defined

# Processing parameters
[processing]
tone_mapping = "dynamic"
metadata_version = {version}          # version is not defined
content_type = {content_format}       # content_format is not defined

# Advanced settings that may trigger warnings
advanced_processing = true
custom_curve_enabled = {curve_enabled}  # curve_enabled is not defined
calibration_mode = {cal_mode}           # cal_mode is not defined
