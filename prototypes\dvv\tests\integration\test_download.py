#!/usr/bin/env python3
"""
Test script for the download functionality of PQ Config Validator
Author: <PERSON>
"""

import sys
import asyncio
import json
from pathlib import Path

# Add the parent directory to Python path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from main import generate_json_report, generate_csv_report, generate_html_report, generate_markdown_report, ProcessingResult, ParameterResult, ValidationWarning

def test_download_functionality():
    """Test the download report generation functionality"""
    print("Testing PQ Config Validator Download Functionality")
    
    # Create sample test data
    undefined_params = [
        ParameterResult(
            parameter_name="ssl_enabled",
            line_number=15,
            line_content="ssl_enabled = {ssl_enabled}"
        ),
        ParameterResult(
            parameter_name="db_password",
            line_number=21,
            line_content="password = {db_password}"
        ),
        ParameterResult(
            parameter_name="api_key",
            line_number=26,
            line_content="key = {api_key}"
        )
    ]
    
    validation_warnings = [
        ValidationWarning(
            warning_type="range_violation",
            message="Bx color primary (0.154700) is outside of the supported range. Clipped to [0.123047, 0.154297]",
            parameter="Bx color primary",
            value="0.154700",
            suggested_range="0.123047, 0.154297"
        ),
        ValidationWarning(
            warning_type="validation_warning",
            message="Display gamma value may not be optimal for HDR content",
            parameter="display_gamma",
            value="2.4",
            suggested_range=None
        )
    ]
    
    # Create sample result
    result = ProcessingResult(
        success=True,
        message="Analysis complete. Found 3 undefined parameter(s) and 2 validation warning(s).",
        undefined_parameters=undefined_params,
        validation_warnings=validation_warnings,
        external_tool_used=True,
        file_path="test_config.cfg"
    )
    
    user_id = "test_user"
    filename = "sample_config.cfg"

    print("\n1. Testing HTML Report Generation:")
    print("-" * 40)
    html_report = generate_html_report(result, user_id, filename)
    print("HTML Report Preview (first 300 chars):")
    print(html_report[:300] + "...")
    print(f"✅ HTML report generated ({len(html_report)} characters)")

    print("\n2. Testing Markdown Report Generation:")
    print("-" * 40)
    markdown_report = generate_markdown_report(result, user_id, filename)
    md_lines = markdown_report.split('\n')
    print("Markdown Report Preview (first 15 lines):")
    for i, line in enumerate(md_lines[:15]):
        print(f"   {i+1:2d}: {line}")
    print(f"   ... (total {len(md_lines)} lines)")

    print("\n3. Testing JSON Report Generation:")
    print("-" * 40)
    json_report = generate_json_report(result, user_id, filename)
    print("JSON Report Preview (first 200 chars):")
    print(json_report[:200] + "...")

    # Validate JSON structure
    try:
        json_data = json.loads(json_report)
        print(f"✅ JSON is valid")
        print(f"   - Metadata includes author: {json_data['metadata']['author']}")
        print(f"   - Undefined parameters: {len(json_data['undefined_parameters'])}")
        print(f"   - Validation warnings: {len(json_data['validation_warnings'])}")
    except json.JSONDecodeError as e:
        print(f"[ERROR] JSON is invalid: {e}")

    print("\n4. Testing CSV Report Generation:")
    print("-" * 40)
    csv_report = generate_csv_report(result, user_id, filename)
    csv_lines = csv_report.split('\n')
    print("CSV Report Preview (first 8 lines):")
    for i, line in enumerate(csv_lines[:8]):
        print(f"   {i+1:2d}: {line}")
    print(f"   ... (total {len(csv_lines)} lines)")
    
    print("\n5. Testing Report Content Validation:")
    print("-" * 40)

    # Check if all reports contain required information
    required_info = [
        "PQ Config Validator",
        "Ethan Li",
        user_id,
        filename,
        "ssl_enabled",
        "Bx color primary"
    ]

    reports = {
        "HTML": html_report,
        "Markdown": markdown_report,
        "JSON": json_report,
        "CSV": csv_report
    }

    for report_type, report_content in reports.items():
        print(f"\n{report_type} Report Validation:")
        for info in required_info:
            if info in report_content:
                print(f"   ✅ Contains: {info}")
            else:
                print(f"   [ERROR] Missing: {info}")
    
    print("\n" + "=" * 60)
    print("Download functionality testing completed!")
    print("HTML and Markdown reports generated successfully with author attribution.")
    print("=" * 60)

if __name__ == "__main__":
    test_download_functionality()
    
    print("\nTo test the download feature in the web interface:")
    print("1. Access: http://localhost:8000")
    print("2. Register a user ID")
    print("3. Upload a configuration file")
    print("4. Click the 'Download Report' dropdown")
    print("5. Select HTML Report or Markdown Report (recommended)")
    print("6. Verify the downloaded file contains analysis results with proper formatting")
